import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/public/courses - Get courses for public display (no auth required)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limitParam = searchParams.get('limit')
    const limit = limitParam ? parseInt(limitParam) : undefined
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const level = searchParams.get('level')
    const sortBy = searchParams.get('sortBy') || 'popular'

    // Build where clause for filtering
    const where: any = {
      isActive: true,
      isPublished: true
    }

    if (category && category !== 'all') {
      where.category = { equals: category, mode: 'insensitive' }
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { shortDescription: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (level && level !== 'all') {
      where.level = { equals: level, mode: 'insensitive' }
    }

    // Build orderBy clause
    let orderBy: any = { createdAt: 'desc' }

    switch (sortBy) {
      case 'popular':
        orderBy = { studentsCount: 'desc' }
        break
      case 'rating':
        orderBy = { rating: 'desc' }
        break
      case 'newest':
        orderBy = { createdAt: 'desc' }
        break
      case 'price-asc':
        orderBy = { price: 'asc' }
        break
      case 'price-desc':
        orderBy = { price: 'desc' }
        break
    }

    // Build query options
    const queryOptions: any = {
      where,
      orderBy,
      include: {
        instructor: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        _count: {
          select: {
            enrollments: true
          }
        }
      }
    };

    // Add limit only if specified
    if (limit) {
      queryOptions.take = limit;
    }

    const courses = await prisma.course.findMany(queryOptions)

    // Transform courses to include enrollment count as studentsCount
    const transformedCourses = courses.map(course => ({
      ...course,
      studentsCount: course._count.enrollments,
      _count: undefined // Remove the _count field from response
    }))

    return APIResponse.success({
      courses: transformedCourses,
      total: transformedCourses.length
    })

  } catch (error) {
    console.error('Error fetching public courses:', error)

    // Return empty result instead of error to prevent frontend crashes
    return APIResponse.success({
      courses: [],
      total: 0
    })
  }
}


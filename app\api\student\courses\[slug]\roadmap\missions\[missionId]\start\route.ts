import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// POST /api/student/courses/[slug]/roadmap/missions/[missionId]/start - Start or continue a mission
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    try {
      const courseSlug = params?.slug as string
      const missionId = params?.missionId as string

      if (!courseSlug || !missionId) {
        return APIResponse.badRequest('Course slug and mission ID are required')
      }

      // Find course by slug
      const course = await prisma.course.findUnique({
        where: { slug: courseSlug },
        select: { id: true, hasRoadmap: true }
      })

      if (!course) {
        return APIResponse.notFound('Course not found')
      }

      if (!course.hasRoadmap) {
        return APIResponse.badRequest('This course does not have a roadmap enabled')
      }

      // Check if user is enrolled
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: course.id
          }
        }
      })

      if (!enrollment) {
        return APIResponse.forbidden('You are not enrolled in this course')
      }

      // Find the mission
      const mission = await prisma.courseMission.findUnique({
        where: { id: missionId },
        include: {
          contents: {
            orderBy: { order: 'asc' }
          },
          prerequisites: {
            include: {
              prerequisiteMission: {
                include: {
                  progress: {
                    where: { userId: user.id },
                    take: 1
                  }
                }
              }
            }
          }
        }
      })

      if (!mission || mission.courseId !== course.id) {
        return APIResponse.notFound('Mission not found')
      }

      // Check if prerequisites are met
      const prerequisitesMet = mission.prerequisites.every(prereq => 
        prereq.prerequisiteMission.progress[0]?.isCompleted
      )

      if (!prerequisitesMet) {
        return APIResponse.badRequest('Prerequisites not met for this mission')
      }

      // Get or create mission progress
      const existingProgress = await prisma.missionProgress.findUnique({
        where: {
          userId_missionId: {
            userId: user.id,
            missionId: mission.id
          }
        }
      })

      let missionProgress
      if (existingProgress) {
        // Update existing progress
        missionProgress = await prisma.missionProgress.update({
          where: { id: existingProgress.id },
          data: {
            lastAccessAt: new Date(),
            isStarted: true
          }
        })
      } else {
        // Create new progress
        missionProgress = await prisma.missionProgress.create({
          data: {
            userId: user.id,
            missionId: mission.id,
            isStarted: true,
            startedAt: new Date(),
            lastAccessAt: new Date()
          }
        })
      }

      // Find the next content item to navigate to
      let nextContentUrl = null
      if (mission.contents.length > 0) {
        const firstContent = mission.contents[0]
        
        // Generate URL based on content type
        switch (firstContent.contentType) {
          case 'LESSON':
            // Find the lesson to get its URL structure
            const lesson = await prisma.courseLesson.findUnique({
              where: { id: firstContent.contentId },
              include: {
                chapter: {
                  include: {
                    section: true
                  }
                }
              }
            })
            if (lesson) {
              nextContentUrl = `/student/courses/${courseSlug}?lesson=${lesson.id}`
            }
            break
          case 'QUIZ':
            nextContentUrl = `/student/courses/${courseSlug}/quiz/${firstContent.contentId}`
            break
          case 'ASSIGNMENT':
            nextContentUrl = `/student/courses/${courseSlug}/assignment/${firstContent.contentId}`
            break
          default:
            nextContentUrl = `/student/courses/${courseSlug}`
        }
      }

      // Update course enrollment last access
      await prisma.courseEnrollment.update({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: course.id
          }
        },
        data: {
          lastAccessedAt: new Date()
        }
      })

      return APIResponse.success({
        missionProgress: {
          id: missionProgress.id,
          isStarted: missionProgress.isStarted,
          isCompleted: missionProgress.isCompleted,
          completionRate: missionProgress.completionRate,
          pointsEarned: missionProgress.pointsEarned,
          startedAt: missionProgress.startedAt?.toISOString(),
          completedAt: missionProgress.completedAt?.toISOString()
        },
        nextContentUrl,
        message: existingProgress ? 'Mission resumed' : 'Mission started'
      }, existingProgress ? 'Mission resumed successfully' : 'Mission started successfully')

    } catch (error) {
      console.error('Error starting mission:', error)
      return APIResponse.internalServerError('Failed to start mission')
    }
  }
)

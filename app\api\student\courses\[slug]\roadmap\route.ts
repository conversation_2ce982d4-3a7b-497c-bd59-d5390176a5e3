import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/courses/[slug]/roadmap - Get course roadmap with user progress
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    try {
      const courseSlug = params?.slug as string

      if (!courseSlug) {
        return APIResponse.badRequest('Course slug is required')
      }

      // Find course by slug
      const course = await prisma.course.findUnique({
        where: { slug: courseSlug },
        select: {
          id: true,
          title: true,
          slug: true,
          hasRoadmap: true,
          roadmapTitle: true,
          roadmapDescription: true,
          duration: true
        }
      })

      if (!course) {
        return APIResponse.notFound('Course not found')
      }

      if (!course.hasRoadmap) {
        return APIResponse.badRequest('This course does not have a roadmap enabled')
      }

      // Check if user is enrolled
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: course.id
          }
        }
      })

      if (!enrollment) {
        return APIResponse.forbidden('You are not enrolled in this course')
      }

      // Fetch missions with user progress
      const missions = await prisma.courseMission.findMany({
        where: { courseId: course.id },
        include: {
          contents: {
            orderBy: { order: 'asc' }
          },
          prerequisites: {
            include: {
              prerequisiteMission: {
                select: { id: true, title: true }
              }
            }
          },
          progress: {
            where: { userId: user.id },
            take: 1
          }
        },
        orderBy: { order: 'asc' }
      })

      // Transform missions data
      const transformedMissions = missions.map(mission => ({
        id: mission.id,
        title: mission.title,
        description: mission.description,
        icon: mission.icon,
        color: mission.color,
        order: mission.order,
        xPosition: mission.xPosition,
        yPosition: mission.yPosition,
        isRequired: mission.isRequired,
        pointsReward: mission.pointsReward,
        badgeReward: mission.badgeReward,
        estimatedTime: mission.estimatedTime,
        contents: mission.contents,
        prerequisites: mission.prerequisites.map(p => p.prerequisiteMissionId),
        progress: mission.progress[0] ? {
          id: mission.progress[0].id,
          isStarted: mission.progress[0].isStarted,
          isCompleted: mission.progress[0].isCompleted,
          completionRate: mission.progress[0].completionRate,
          pointsEarned: mission.progress[0].pointsEarned,
          startedAt: mission.progress[0].startedAt?.toISOString(),
          completedAt: mission.progress[0].completedAt?.toISOString()
        } : null
      }))

      // Calculate user progress statistics
      const completedMissions = transformedMissions.filter(m => m.progress?.isCompleted).length
      const totalPoints = transformedMissions.reduce((sum, m) => sum + (m.progress?.pointsEarned || 0), 0)
      
      // Calculate current streak (simplified - in production, you'd track daily activity)
      const currentStreak = await calculateUserStreak(user.id, course.id)

      const userProgress = {
        totalPoints,
        completedMissions,
        currentStreak
      }

      return APIResponse.success({
        course: {
          id: course.id,
          title: course.title,
          slug: course.slug,
          hasRoadmap: course.hasRoadmap,
          roadmapTitle: course.roadmapTitle,
          roadmapDescription: course.roadmapDescription,
          estimatedCompletion: course.duration
        },
        missions: transformedMissions,
        userProgress
      }, 'Roadmap data retrieved successfully')

    } catch (error) {
      console.error('Error fetching roadmap data:', error)
      return APIResponse.internalServerError('Failed to fetch roadmap data')
    }
  }
)

// Helper function to calculate user streak
async function calculateUserStreak(userId: string, courseId: string): Promise<number> {
  try {
    // Get recent mission completions
    const recentCompletions = await prisma.missionProgress.findMany({
      where: {
        userId,
        mission: { courseId },
        isCompleted: true,
        completedAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      orderBy: { completedAt: 'desc' },
      select: { completedAt: true }
    })

    if (recentCompletions.length === 0) return 0

    // Calculate consecutive days with activity
    let streak = 0
    let currentDate = new Date()
    currentDate.setHours(0, 0, 0, 0)

    const completionDates = recentCompletions
      .map(c => {
        const date = new Date(c.completedAt!)
        date.setHours(0, 0, 0, 0)
        return date.getTime()
      })
      .filter((date, index, arr) => arr.indexOf(date) === index) // Remove duplicates
      .sort((a, b) => b - a) // Sort descending

    for (const completionTime of completionDates) {
      if (completionTime === currentDate.getTime()) {
        streak++
        currentDate.setDate(currentDate.getDate() - 1)
      } else if (completionTime === currentDate.getTime() + 24 * 60 * 60 * 1000) {
        // Allow for yesterday if today has no activity
        streak++
        currentDate.setDate(currentDate.getDate() - 1)
      } else {
        break
      }
    }

    return streak
  } catch (error) {
    console.error('Error calculating streak:', error)
    return 0
  }
}

import { NextRequest } from 'next/server'
import { Server as SocketIOServer } from 'socket.io'
import { Server as HTTPServer } from 'http'


// Global socket server instance
let io: SocketIOServer | null = null

// Store connected users
const connectedUsers = new Map<string, {
  socketId: string
  userId: string
  name: string
  email: string
  role: string
  joinedAt: Date
  lastSeen: Date
}>()

// Store active rooms
const activeRooms = new Map<string, Set<string>>()

export async function GET(request: NextRequest) {
  if (!io) {
    // Initialize Socket.IO server
    const httpServer = new HTTPServer()
    io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      path: '/api/socket',
      transports: ['websocket', 'polling']
    })

    // Socket connection handling
    io.on('connection', (socket) => {
             // Handle user authentication and registration
      socket.on('authenticate', async (userData: {
        userId: string
        name: string
        email: string
        role: string
        token?: string
      }) => {
        try {
          // Store user connection info
          connectedUsers.set(socket.id, {
            socketId: socket.id,
            userId: userData.userId,
            name: userData.name,
            email: userData.email,
            role: userData.role,
            joinedAt: new Date(),
            lastSeen: new Date()
          })

          // Join user to their personal room
          socket.join(`user:${userData.userId}`)
          
          // Join role-based rooms
          socket.join(`role:${userData.role}`)

          // Emit successful authentication
          socket.emit('authenticated', {
            success: true,
            userId: userData.userId,
            connectedUsers: Array.from(connectedUsers.values()).length
          })

          // Broadcast user joined to admins
          socket.to('role:ADMIN').emit('user:joined', {
            userId: userData.userId,
            name: userData.name,
            role: userData.role,
            joinedAt: new Date()
          })

                   } catch (error) {
          console.error('Authentication error:', error)
          socket.emit('authentication_error', { message: 'Authentication failed' })
        }
      })

      // Handle sending notifications
      socket.on('send_notification', (data: {
        targetUserId?: string
        type: 'info' | 'success' | 'warning' | 'error'
        title: string
        message: string
        data?: any
        broadcast?: boolean
      }) => {
        const user = connectedUsers.get(socket.id)
        if (!user || user.role !== 'ADMIN') {
          socket.emit('error', { message: 'Unauthorized to send notifications' })
          return
        }

        const notification = {
          id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data || {},
          createdAt: new Date(),
          senderId: user.userId,
          senderName: user.name
        }

        if (data.broadcast) {
          // Broadcast to all users
          io!.emit('notification', notification)
                   } else if (data.targetUserId) {
          // Send to specific user
          io!.to(`user:${data.targetUserId}`).emit('notification', notification)
                   }

        // Confirm to sender
        socket.emit('notification_sent', {
          success: true,
          notification,
          target: data.broadcast ? 'all' : data.targetUserId
        })
      })

      // Handle quiz progress updates
      socket.on('quiz_progress', (data: {
        quizId: string
        questionIndex: number
        timeRemaining: number
        answered: boolean
        userId?: string
      }) => {
        const user = connectedUsers.get(socket.id)
        if (!user) return

        const progressUpdate = {
          ...data,
          userId: user.userId,
          userName: user.name,
          timestamp: new Date()
        }

        // Broadcast to quiz room and admins
        socket.to(`quiz:${data.quizId}`).emit('quiz_progress_update', progressUpdate)
        socket.to('role:ADMIN').emit('quiz_progress_update', progressUpdate)
      })

      // Handle joining quiz rooms
      socket.on('join_quiz', (quizId: string) => {
        socket.join(`quiz:${quizId}`)
        
        if (!activeRooms.has(`quiz:${quizId}`)) {
          activeRooms.set(`quiz:${quizId}`, new Set())
        }
        activeRooms.get(`quiz:${quizId}`)!.add(socket.id)

               })

      // Handle leaving quiz rooms
      socket.on('leave_quiz', (quizId: string) => {
        socket.leave(`quiz:${quizId}`)
        
        if (activeRooms.has(`quiz:${quizId}`)) {
          activeRooms.get(`quiz:${quizId}`)!.delete(socket.id)
          if (activeRooms.get(`quiz:${quizId}`)!.size === 0) {
            activeRooms.delete(`quiz:${quizId}`)
          }
        }

               })

      // Handle chat messages
      socket.on('chat_message', (data: {
        roomId: string
        message: string
        type?: 'text' | 'image' | 'file'
      }) => {
        const user = connectedUsers.get(socket.id)
        if (!user) return

        const chatMessage = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userId: user.userId,
          userName: user.name,
          message: data.message,
          type: data.type || 'text',
          timestamp: new Date(),
          roomId: data.roomId
        }

        // Broadcast to room
        io!.to(data.roomId).emit('chat_message', chatMessage)
      })

      // Handle real-time metrics requests
      socket.on('get_metrics', () => {
        const user = connectedUsers.get(socket.id)
        if (!user || user.role !== 'ADMIN') {
          socket.emit('error', { message: 'Unauthorized to access metrics' })
          return
        }

        const metrics = {
          connectedUsers: connectedUsers.size,
          activeRooms: activeRooms.size,
          usersByRole: {
            ADMIN: Array.from(connectedUsers.values()).filter(u => u.role === 'ADMIN').length,
            STUDENT: Array.from(connectedUsers.values()).filter(u => u.role === 'STUDENT').length
          },
          timestamp: new Date()
        }

        socket.emit('metrics_update', metrics)
      })

      // Handle quiz events
      socket.on('quiz:join', (data: { quizId: string }) => {
        const user = connectedUsers.get(socket.id)
        if (!user) return

        const roomId = `quiz:${data.quizId}`
        socket.join(roomId)

        // Get current participants in the quiz
        const participants = Array.from(connectedUsers.values())
          .filter(u => io!.sockets.adapter.rooms.get(roomId)?.has(u.socketId))
          .map(u => ({
            id: u.userId,
            name: u.name,
            email: u.email,
            role: u.role,
            socketId: u.socketId,
            joinedAt: u.joinedAt,
            lastSeen: u.lastSeen
          }))

        // Emit to the user who joined
        socket.emit('quiz:joined', {
          sessionId: roomId,
          quizId: data.quizId,
          participantCount: participants.length,
          participants
        })

        // Notify other participants
        socket.to(roomId).emit('quiz:participant_joined', {
          userId: user.userId,
          name: user.name,
          participantCount: participants.length
        })

               })

      socket.on('quiz:leave', (data: { quizId: string }) => {
        const user = connectedUsers.get(socket.id)
        if (!user) return

        const roomId = `quiz:${data.quizId}`
        socket.leave(roomId)

        // Get remaining participants
        const remainingCount = io!.sockets.adapter.rooms.get(roomId)?.size || 0

        // Notify other participants
        socket.to(roomId).emit('quiz:participant_left', {
          userId: user.userId,
          participantCount: remainingCount
        })

               })

      socket.on('quiz:progress', (data: {
        quizId: string
        questionIndex: number
        timeRemaining: number
        answered: boolean
      }) => {
        const user = connectedUsers.get(socket.id)
        if (!user) return

        const roomId = `quiz:${data.quizId}`

        // Broadcast progress to other participants in the same quiz
        socket.to(roomId).emit('quiz:participant_progress', {
          userId: user.userId,
          questionIndex: data.questionIndex,
          timeRemaining: data.timeRemaining,
          answered: data.answered
        })
      })

      socket.on('quiz:completed', (data: {
        quizId: string
        score: number
        timeSpent: number
        rank?: number
      }) => {
        const user = connectedUsers.get(socket.id)
        if (!user) return

        const roomId = `quiz:${data.quizId}`

        // Broadcast completion to other participants
        socket.to(roomId).emit('quiz:participant_completed', {
          userId: user.userId,
          score: data.score,
          timeSpent: data.timeSpent,
          rank: data.rank
        })

      })

      // Handle disconnection
      socket.on('disconnect', () => {
        const user = connectedUsers.get(socket.id)
        if (user) {
          // Remove from all rooms
          activeRooms.forEach((users, roomId) => {
            users.delete(socket.id)
            if (users.size === 0) {
              activeRooms.delete(roomId)
            }
          })

          // Broadcast user left to admins
          socket.to('role:ADMIN').emit('user:left', {
            userId: user.userId,
            name: user.name,
            role: user.role,
            leftAt: new Date()
          })

          connectedUsers.delete(socket.id)
        }
      })

      // Send initial connection success
      socket.emit('connected', {
        socketId: socket.id,
        timestamp: new Date()
      })
    })

    // Start the HTTP server
    const port = process.env.SOCKET_PORT || 3001
    httpServer.listen(port, () => {
      console.log(`Socket.IO server running on port ${port}`)     })
  }

  return new Response(JSON.stringify({
    success: true,
    message: 'Socket server initialized',
    connectedUsers: connectedUsers.size
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  })
}

// Export the socket server instance for use in other parts of the app
export { io }

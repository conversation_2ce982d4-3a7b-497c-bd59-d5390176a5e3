'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon,
  TrophyIcon,
  ShareIcon,
  ArrowDownTrayIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

interface Certificate {
  id: string
  certificateId: string
  issuedAt: string
  completionDate: string
  finalScore?: number
  pdfUrl?: string
  studentName?: string
  course: {
    id: string
    title: string
    instructor: {
      id: string
      name: string
    }
  }
}

interface CertificateModalProps {
  isOpen: boolean
  onClose: () => void
  courseId: string
  courseName: string
  instructorName: string
}

export default function CertificateModal({
  isOpen,
  onClose,
  courseId,
  courseName,
  instructorName
}: CertificateModalProps) {
  const [certificate, setCertificate] = useState<Certificate | null>(null)
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [studentName, setStudentName] = useState<string>('')

  useEffect(() => {
    if (isOpen && courseId) {
      fetchCertificate()
      fetchStudentName()
    }
  }, [isOpen, courseId])

  const fetchStudentName = async () => {
    try {
      const response = await fetch('/api/student/profile')
      if (response.ok) {
        const data = await response.json()
        setStudentName(data.data?.name || 'Student')
      }
    } catch (error) {
      console.error('Error fetching student name:', error)
      setStudentName('Student')
    }
  }

  const fetchCertificate = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/student/courses/certificates?courseId=${courseId}`)

      if (response.ok) {
        const data = await response.json()
                 setCertificate(data.data?.certificate || data.certificate)
      } else if (response.status === 404) {
        // Certificate doesn't exist yet
                 setCertificate(null)
      } else {
        throw new Error('Failed to fetch certificate')
      }
    } catch (error) {
      console.error('Error fetching certificate:', error)
      toast.error('Failed to load certificate')
    } finally {
      setLoading(false)
    }
  }

  const generateCertificate = async () => {
    try {
      setGenerating(true)
      const response = await fetch('/api/student/courses/certificates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ courseId })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to generate certificate')
      }

      const data = await response.json()
             setCertificate(data.data?.certificate || data.certificate)
      toast.success('Certificate generated successfully!')
    } catch (error: any) {
      console.error('Error generating certificate:', error)
      toast.error(error.message || 'Failed to generate certificate')
    } finally {
      setGenerating(false)
    }
  }

  const downloadCertificate = () => {
    if (certificate?.pdfUrl) {
      window.open(certificate.pdfUrl, '_blank')
    } else {
      toast.error('Certificate PDF not available')
    }
  }

  const shareCertificate = async () => {
    if (navigator.share && certificate) {
      try {
        await navigator.share({
          title: `Certificate - ${courseName}`,
          text: `I've completed the course "${courseName}" and earned a certificate!`,
          url: window.location.href
        })
      } catch (error) {
        // Fallback to copying to clipboard
        copyToClipboard()
      }
    } else {
      copyToClipboard()
    }
  }

  const copyToClipboard = () => {
    if (certificate) {
      const text = `I've completed the course "${courseName}" and earned a certificate! Certificate ID: ${certificate.certificateId}`
      navigator.clipboard.writeText(text).then(() => {
        toast.success('Certificate details copied to clipboard!')
      }).catch(() => {
        toast.error('Failed to copy to clipboard')
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative w-full max-w-2xl bg-white rounded-2xl shadow-2xl overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg">
                  <TrophyIcon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">Course Certificate</h2>
                  <p className="text-sm text-gray-600">{courseName}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading certificate...</p>
                </div>
              ) : certificate ? (
                <div className="space-y-6">
                  {/* Certificate Preview */}
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-8 text-center border-4 border-blue-200">
                    <div className="mb-6">
                      <TrophyIcon className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
                      <h3 className="text-2xl font-bold text-gray-800 mb-2">Certificate of Completion</h3>
                      <p className="text-gray-600">This is to certify that</p>
                    </div>
                    
                    <div className="mb-6">
                      <div className="text-3xl font-bold text-blue-600 mb-2">{studentName || 'Student'}</div>
                      <p className="text-gray-600 mb-4">has successfully completed the course</p>
                      <div className="text-xl font-semibold text-gray-800 mb-4">"{courseName}"</div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-6">
                      <div>
                        <div className="font-medium">Completion Date</div>
                        <div>{formatDate(certificate.completionDate)}</div>
                      </div>
                      <div>
                        <div className="font-medium">Instructor</div>
                        <div>{instructorName}</div>
                      </div>
                      {certificate.finalScore && (
                        <div className="col-span-2">
                          <div className="font-medium">Final Score</div>
                          <div>{certificate.finalScore}%</div>
                        </div>
                      )}
                    </div>

                    <div className="border-t border-blue-200 pt-4">
                      <div className="text-xs text-gray-500">
                        Certificate ID: {certificate.certificateId}
                      </div>
                      <div className="text-xs text-gray-500">
                        Issued: {formatDate(certificate.issuedAt)}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-center space-x-4">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={downloadCertificate}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                    >
                      <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                      Download PDF
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={shareCertificate}
                      className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                    >
                      <ShareIcon className="w-4 h-4 mr-2" />
                      Share
                    </motion.button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <TrophyIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">Generate Your Certificate</h3>
                  <p className="text-gray-600 mb-6">
                    Congratulations on completing the course! Generate your certificate to celebrate your achievement.
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={generateCertificate}
                    disabled={generating}
                    className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
                  >
                    {generating ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Generating...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <TrophyIcon className="w-5 h-5 mr-2" />
                        Generate Certificate
                      </div>
                    )}
                  </motion.button>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}

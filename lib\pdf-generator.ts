import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, PDFOptions } from 'puppeteer'

export interface PDFGenerationOptions {
  format?: 'A4' | 'Letter'
  landscape?: boolean
  margin?: {
    top?: string
    right?: string
    bottom?: string
    left?: string
  }
  printBackground?: boolean
  quality?: number
}

export class PDFGenerator {
  private static instance: PDFGenerator
  private browser: Browser | null = null

  private constructor() {}

  public static getInstance(): PDFGenerator {
    if (!PDFGenerator.instance) {
      PDFGenerator.instance = new PDFGenerator()
    }
    return PDFGenerator.instance
  }

  // Removed initBrowser method - now using per-request browser instances

  public async generatePDFFromHTML(
    html: string,
    options: PDFGenerationOptions = {}
  ): Promise<Buffer> {
    let browser: Browser | null = null
    let page: Page | null = null

    try {
             // Create a new browser instance for each PDF generation to avoid conflicts
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ],
        timeout: 30000 // 30 second timeout
      })

      page = await browser.newPage()

      // Set viewport for consistent rendering
      await page.setViewport({
        width: 1200,
        height: 850,
        deviceScaleFactor: 2
      })

      // Set content with timeout
      await page.setContent(html, {
        waitUntil: ['networkidle0', 'domcontentloaded'],
        timeout: 30000
      })

      // Wait for fonts to load with timeout
      await Promise.race([
        page.evaluateHandle('document.fonts.ready'),
        new Promise(resolve => setTimeout(resolve, 5000)) // 5 second timeout for fonts
      ])

      // Additional wait to ensure all styles are applied
      await new Promise(resolve => setTimeout(resolve, 1000))

             const pdfOptions: PDFOptions = {
        format: options.format || 'A4',
        landscape: options.landscape || true, // Landscape for certificate
        printBackground: options.printBackground !== false,
        margin: options.margin || {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in'
        },
        preferCSSPageSize: true,
        timeout: 30000 // 30 second timeout for PDF generation
      }

      const pdfBuffer = await page.pdf(pdfOptions)

             return Buffer.from(pdfBuffer)

    } catch (error) {
      console.error('Error generating PDF:', error)
      throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      // Ensure proper cleanup
      try {
        if (page && !page.isClosed()) {
          await page.close()
        }
      } catch (closeError) {
        console.warn('Error closing page:', closeError)
      }

      try {
        if (browser) {
          await browser.close()
        }
      } catch (closeError) {
        console.warn('Error closing browser:', closeError)
      }
    }
  }

  public async generateCertificatePDF(html: string): Promise<Buffer> {
    return this.generatePDFFromHTML(html, {
      format: 'A4',
      landscape: true,
      printBackground: true,
      margin: {
        top: '0.2in',
        right: '0.2in',
        bottom: '0.2in',
        left: '0.2in'
      }
    })
  }

  public async closeBrowser(): Promise<void> {
    if (this.browser) {
             await this.browser.close()
      this.browser = null
           }
  }

  // Cleanup method for graceful shutdown
  public static async cleanup(): Promise<void> {
    if (PDFGenerator.instance) {
      await PDFGenerator.instance.closeBrowser()
    }
  }
}

// Export a convenience function for quick PDF generation
export async function generatePDFFromHTML(
  html: string,
  options?: PDFGenerationOptions
): Promise<Buffer> {
  const generator = PDFGenerator.getInstance()
  return generator.generatePDFFromHTML(html, options)
}

// Export a convenience function for certificate PDF generation
export async function generateCertificatePDF(html: string): Promise<Buffer> {
  const generator = PDFGenerator.getInstance()
  return generator.generateCertificatePDF(html)
}

// Graceful shutdown handler
process.on('SIGINT', async () => {
     await PDFGenerator.cleanup()
  process.exit(0)
})

process.on('SIGTERM', async () => {
     await PDFGenerator.cleanup()
  process.exit(0)
})

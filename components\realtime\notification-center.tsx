"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

import { 
  Bell, 
  BellRing, 
  X, 
  Check, 
  Info, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Settings,
  Trash2,
 
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getSocketClient } from "@/lib/socket-client"
import { toast } from "@/lib/toast-utils"

interface NotificationData {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  userId?: string
  data?: any
  actionUrl?: string
  imageUrl?: string
  priority?: string
  category?: string
  createdAt: Date
  read?: boolean
  clicked?: boolean
  dismissed?: boolean
}

interface NotificationCenterProps {
  className?: string
}

export function NotificationCenter({ className = "" }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isOpen, setIsOpen] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(false)






  // Fetch notifications from API
  const fetchNotifications = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/notifications?limit=50')
      if (response.ok) {
        const data = await response.json()


        if (data.notifications && Array.isArray(data.notifications)) {
          const formattedNotifications = data.notifications.map((item: any) => ({
            id: item.id,
            type: item.notification.type.toLowerCase(),
            title: item.notification.title,
            message: item.notification.message,
            data: item.notification.data,
            actionUrl: item.notification.actionUrl,
            imageUrl: item.notification.imageUrl,
            priority: item.notification.priority,
            category: item.notification.category,
            createdAt: new Date(item.createdAt),
            read: item.isRead,
            clicked: item.isClicked,
            dismissed: item.isDismissed
          }))
          setNotifications(formattedNotifications)
        } else {
          setNotifications([])
        }
      } else {
        console.error('Failed to fetch notifications:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Fetch unread count
  const fetchUnreadCount = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/unread-count')
      if (response.ok) {
        const data = await response.json()
        setUnreadCount(data.count)
      }
    } catch (error) {
      console.error('Error fetching unread count:', error)
    }
  }, [])

  useEffect(() => {
    let isComponentMounted = true

    // Load initial data
    fetchNotifications()
    fetchUnreadCount()

    // Initialize real-time socket connection
    const socketClient = getSocketClient()

    // Handle connection status
    const handleConnectionEstablished = () => {
      if (isComponentMounted) {
        setIsConnected(true)

      }
    }

    const handleConnectionLost = () => {
      if (isComponentMounted) {
        setIsConnected(false)

      }
    }

    // Handle real-time notifications
    const handleNotificationReceived = (notification: any) => {
      if (isComponentMounted) {


        // Format notification to match our interface
        const formattedNotification: NotificationData = {
          id: notification.id,
          type: notification.type || 'info',
          title: notification.title,
          message: notification.message,
          userId: notification.userId,
          data: notification.data,
          actionUrl: notification.actionUrl,
          imageUrl: notification.imageUrl,
          priority: notification.priority,
          category: notification.category,
          createdAt: notification.createdAt ? new Date(notification.createdAt) : new Date(),
          read: false,
          clicked: false,
          dismissed: false
        }

        // Add to notifications list
        setNotifications(prev => [formattedNotification, ...prev])
        setUnreadCount(prev => prev + 1)

        // Show toast notification
        toast.info(notification.title, {
          description: notification.message,
          duration: 5000
        })
      }
    }

    // Listen for socket events
    socketClient.on('connection:established', handleConnectionEstablished)
    socketClient.on('connection:lost', handleConnectionLost)
    socketClient.on('notification:received', handleNotificationReceived)

    // Set initial connection status
    setIsConnected(socketClient.isConnected())

    // Cleanup
    return () => {
      isComponentMounted = false
      socketClient.off('connection:established', handleConnectionEstablished)
      socketClient.off('connection:lost', handleConnectionLost)
      socketClient.off('notification:received', handleNotificationReceived)
    }
  }, [])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      default:
        return <Info className="h-4 w-4 text-blue-600" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
      default:
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20'
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'mark_read' })
      })

      if (response.ok) {
        setNotifications(prev =>
          prev.map(n =>
            n.id === notificationId
              ? { ...n, read: true }
              : n
          )
        )
        setUnreadCount(prev => Math.max(0, prev - 1))
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
      toast.error('Failed to mark notification as read')
    }
  }

  const markAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.read)

      // Mark all unread notifications as read
      await Promise.all(
        unreadNotifications.map(notification =>
          fetch(`/api/notifications/${notification.id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: 'mark_read' })
          })
        )
      )

      setNotifications(prev => prev.map(n => ({ ...n, read: true })))
      setUnreadCount(0)
      toast.success('All notifications marked as read')
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      toast.error('Failed to mark all notifications as read')
    }
  }

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
    setUnreadCount(prev => {
      const notification = notifications.find(n => n.id === notificationId)
      return notification && !notification.read ? Math.max(0, prev - 1) : prev
    })
  }

  const clearAllNotifications = () => {
    setNotifications([])
    setUnreadCount(0)
  }

  const formatTime = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - new Date(date).getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  return (
    <div className={`relative ${className}`}>
      {/* Notification Bell */}
      <Button
        variant="ghost"
        size="sm"
        className="relative"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isConnected && unreadCount > 0 ? (
          <BellRing className="h-5 w-5" />
        ) : (
          <Bell className="h-5 w-5" />
        )}

        {unreadCount > 0 && (
          <Badge
            className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500 hover:bg-red-600"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}

        {/* Connection indicator */}
        <div className={`absolute -bottom-1 -right-1 w-2 h-2 rounded-full ${
          isConnected ? 'bg-green-500' : 'bg-red-500'
        }`} />
      </Button>

      {/* Notification Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-full mt-2 w-96 z-30"
          >
            <Card className="shadow-lg border-2">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">Notifications</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${
                        isConnected ? 'bg-green-500' : 'bg-red-500'
                      }`} />
                      {isConnected ? 'Connected' : 'Disconnected'}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsOpen(false)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Actions */}
                {notifications.length > 0 && (
                  <div className="flex items-center gap-2 pt-2">
                    {unreadCount > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={markAllAsRead}
                      >
                        <Check className="h-3 w-3 mr-1" />
                        Mark all read
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearAllNotifications}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Clear all
                    </Button>
                  </div>
                )}
              </CardHeader>

              <CardContent className="p-0">
                {notifications.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Bell className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>No notifications yet</p>
                    <p className="text-sm">You'll see real-time updates here</p>
                  </div>
                ) : (
                  <ScrollArea className="h-96">
                    <div className="space-y-1 p-3">
                      {notifications.map((notification, index) => (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: 0.05 * index }}
                          className={`p-3 rounded-lg border transition-all hover:shadow-sm ${
                            getNotificationColor(notification.type)
                          } ${!notification.read ? 'border-l-4' : ''}`}
                        >
                          <div className="flex items-start gap-3">
                            <div className="flex-shrink-0 mt-0.5">
                              {getNotificationIcon(notification.type)}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <h4 className="font-medium text-sm">
                                  {notification.title}
                                </h4>
                                <div className="flex items-center gap-1 ml-2">
                                  <span className="text-xs text-muted-foreground">
                                    {formatTime(notification.createdAt)}
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => deleteNotification(notification.id)}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                              
                              <p className="text-sm text-muted-foreground mt-1">
                                {notification.message}
                              </p>

                              {notification.data && (
                                <div className="mt-2">
                                  <Badge variant="outline" className="text-xs">
                                    {notification.data.action || 'View Details'}
                                  </Badge>
                                </div>
                              )}

                              {!notification.read && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="mt-2 h-6 text-xs"
                                  onClick={() => markAsRead(notification.id)}
                                >
                                  <Check className="h-3 w-3 mr-1" />
                                  Mark as read
                                </Button>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

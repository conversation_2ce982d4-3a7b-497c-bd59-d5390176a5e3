import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

// Email configuration
const createTransporter = () => {
  return nodemailer.createTransporter({
    service: 'gmail', // or your preferred email service
    auth: {
      user: process.env.EMAIL_USER, // Your email
      pass: process.env.EMAIL_PASS, // Your email password or app password
    },
  });
};

// WhatsApp API function (using a service like Twilio or similar)
const sendWhatsAppMessage = async (message: string) => {
  try {
    // Replace with your WhatsApp API implementation
    // Example using Twilio WhatsApp API:
    /*
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = require('twilio')(accountSid, authToken);
    
    await client.messages.create({
      body: message,
      from: 'whatsapp:+***********', // Your Twilio WhatsApp number
      to: 'whatsapp:+************' // Your WhatsApp number
    });
    */
    
    // For now, we'll just log the message
         return true;
  } catch (error) {
    console.error('WhatsApp send error:', error);
    return false;
  }
};

export async function POST(request: NextRequest) {
  try {
    const body: ContactFormData = await request.json();
    const { name, email, phone, subject, message } = body;

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create email content
    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">New Contact Form Submission</h1>
        </div>
        
        <div style="padding: 20px; background: #f9f9f9;">
          <h2 style="color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Contact Details</h2>
          
          <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr>
              <td style="padding: 10px; font-weight: bold; background: #e9ecef; width: 30%;">Name:</td>
              <td style="padding: 10px; background: white;">${name}</td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; background: #e9ecef;">Email:</td>
              <td style="padding: 10px; background: white;">${email}</td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; background: #e9ecef;">Phone:</td>
              <td style="padding: 10px; background: white;">${phone || 'Not provided'}</td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; background: #e9ecef;">Subject:</td>
              <td style="padding: 10px; background: white;">${subject}</td>
            </tr>
          </table>
          
          <h3 style="color: #333; margin-top: 30px;">Message:</h3>
          <div style="background: white; padding: 15px; border-left: 4px solid #667eea; margin: 10px 0;">
            ${message.replace(/\n/g, '<br>')}
          </div>
          
          <div style="margin-top: 30px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
            <strong>📅 Submitted:</strong> ${new Date().toLocaleString()}
          </div>
        </div>
        
        <div style="background: #333; color: white; padding: 15px; text-align: center;">
          <p style="margin: 0;">PrepLocus Contact Form - Automated Message</p>
        </div>
      </div>
    `;

    // WhatsApp message content
    const whatsappMessage = `
🔔 *New Contact Form Submission*

👤 *Name:* ${name}
📧 *Email:* ${email}
📱 *Phone:* ${phone || 'Not provided'}
📋 *Subject:* ${subject}

💬 *Message:*
${message}

🕒 *Time:* ${new Date().toLocaleString()}
    `.trim();

    // Send email notification
    try {
      const transporter = createTransporter();
      
      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: process.env.CONTACT_EMAIL || process.env.EMAIL_USER, // Where to receive contact form emails
        subject: `Contact Form: ${subject} - ${name}`,
        html: emailContent,
      });

           } catch (emailError) {
      console.error('Email send error:', emailError);
      // Don't fail the request if email fails
    }

    // Send WhatsApp notification
    try {
      await sendWhatsAppMessage(whatsappMessage);
           } catch (whatsappError) {
      console.error('WhatsApp send error:', whatsappError);
      // Don't fail the request if WhatsApp fails
    }

    // Send confirmation email to the user
    try {
      const transporter = createTransporter();
      
      const confirmationEmail = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">Thank You for Contacting Us!</h1>
          </div>
          
          <div style="padding: 20px;">
            <p>Dear ${name},</p>
            
            <p>Thank you for reaching out to us. We have received your message and will get back to you within 24 hours.</p>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0;">Your Message Summary:</h3>
              <p><strong>Subject:</strong> ${subject}</p>
              <p><strong>Message:</strong> ${message}</p>
            </div>
            
            <p>If you have any urgent questions, feel free to call us at <strong>+91 98765 43210</strong>.</p>
            
            <p>Best regards,<br>The PrepLocus Team</p>
          </div>
          
          <div style="background: #333; color: white; padding: 15px; text-align: center;">
            <p style="margin: 0;">PrepLocus - Your Success, Our Mission</p>
          </div>
        </div>
      `;

      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: email,
        subject: 'Thank you for contacting PrepLocus',
        html: confirmationEmail,
      });

           } catch (confirmationError) {
      console.error('Confirmation email error:', confirmationError);
      // Don't fail the request if confirmation email fails
    }

    return NextResponse.json(
      { 
        message: 'Contact form submitted successfully',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import {
  MessageCircle,
  Users,
  Settings,
  Hash,
  Lock,
  Globe,
  HelpCircle,
  BookOpen,
  Trophy,
  Activity
} from "lucide-react"
import { ChatRoom } from "@/components/realtime/chat-room"
import { ConnectionStatus } from "@/components/realtime/connection-status"
import { getSocketClient } from "@/lib/socket-client"
import { toast } from "@/lib/toast-utils"

export default function StudentChatPage() {
  const { data: session } = useSession()
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting')
  const [isPageReady, setIsPageReady] = useState(false)

  useEffect(() => {
    if (session?.user) {
      // Get existing socket client (already initialized in layout)
      const socketClient = getSocketClient()

      // Listen for connection events
      const handleConnectionEstablished = () => {
        setConnectionStatus('connected')
        toast.success('Connected to chat system')
      }

      const handleConnectionLost = () => {
        setConnectionStatus('disconnected')
        toast.error('Lost connection to chat server')
      }

      const handleAuthenticated = (data: any) => {
                setConnectionStatus('connected')
      }

      socketClient.on('connection:established', handleConnectionEstablished)
      socketClient.on('connection:lost', handleConnectionLost)
      socketClient.on('authenticated', handleAuthenticated)

      // Set initial connection status
      setConnectionStatus(socketClient.isConnected() ? 'connected' : 'connecting')

      // Mark page as ready after socket setup
      setIsPageReady(true)

      // Cleanup
      return () => {
        socketClient.off('connection:established', handleConnectionEstablished)
        socketClient.off('connection:lost', handleConnectionLost)
        socketClient.off('authenticated', handleAuthenticated)
      }
    }
  }, [session?.user?.id])

  if (!session?.user || !isPageReady) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            {!session?.user ? 'Authentication Required' : 'Loading Chat...'}
          </h3>
          <p className="text-muted-foreground">
            {!session?.user ? 'Please sign in to access the chat system.' : 'Setting up real-time connection...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-8 min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      {/* Header */}
      <div className="relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

        <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <MessageCircle className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Student Chat
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Connect with other students and get help in real-time
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Badge
                variant={connectionStatus === 'connected' ? 'default' : 'destructive'}
                className="px-4 py-2 text-sm font-medium shadow-lg"
              >
                {connectionStatus === 'connected' ? '🟢 Connected' : `🔴 ${connectionStatus}`}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-xl p-2 rounded-xl">
          <TabsTrigger
            value="general"
            className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
          >
            <Globe className="h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger
            value="study-help"
            className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
          >
            <HelpCircle className="h-4 w-4" />
            Study Help
          </TabsTrigger>
          <TabsTrigger
            value="quiz-discussion"
            className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
          >
            <BookOpen className="h-4 w-4" />
            Quiz Discussion
          </TabsTrigger>
          <TabsTrigger
            value="connection"
            className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
          >
            <Activity className="h-4 w-4" />
            Connection
          </TabsTrigger>
        </TabsList>

        {/* General Chat */}
        <TabsContent value="general" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChatRoom
              key="student-general"
              roomId="student-general"
              roomName="General Discussion"
              roomType="public"
              className="h-[500px]"
            />
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Chat Guidelines
                </CardTitle>
                <CardDescription>
                  Please follow these guidelines for a positive experience
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                    <div>
                      <strong>Be respectful:</strong> Treat all students and instructors with respect
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                    <div>
                      <strong>Stay on topic:</strong> Keep discussions relevant to studies and learning
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2" />
                    <div>
                      <strong>Help others:</strong> Share knowledge and assist fellow students
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2" />
                    <div>
                      <strong>No spam:</strong> Avoid repetitive messages or irrelevant content
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Study Help Chat */}
        <TabsContent value="study-help" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChatRoom
              key="student-study-help"
              roomId="student-study-help"
              roomName="Study Help & Questions"
              roomType="public"
              className="h-[500px]"
            />
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HelpCircle className="h-5 w-5" />
                  Getting Help
                </CardTitle>
                <CardDescription>
                  Tips for getting the best help from the community
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                    <div>
                      <strong>Be specific:</strong> Clearly describe your question or problem
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                    <div>
                      <strong>Show your work:</strong> Share what you've tried so far
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2" />
                    <div>
                      <strong>Be patient:</strong> Wait for responses and thank helpers
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2" />
                    <div>
                      <strong>Pay it forward:</strong> Help others when you can
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Quiz Discussion Chat */}
        <TabsContent value="quiz-discussion" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChatRoom
              key="student-quiz-discussion"
              roomId="student-quiz-discussion"
              roomName="Quiz Discussion"
              roomType="quiz"
              className="h-[500px]"
            />
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  Quiz Discussion Rules
                </CardTitle>
                <CardDescription>
                  Guidelines for discussing quizzes and practice sessions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2" />
                    <div>
                      <strong>No direct answers:</strong> Don't share exact quiz answers
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                    <div>
                      <strong>Discuss concepts:</strong> Focus on understanding topics and methods
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                    <div>
                      <strong>Share strategies:</strong> Discuss study techniques and approaches
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2" />
                    <div>
                      <strong>Encourage others:</strong> Support fellow students in their learning journey
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Connection Status */}
        <TabsContent value="connection" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Real-time Connection Status
              </CardTitle>
              <CardDescription>
                Monitor your connection to the chat system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ConnectionStatus showDetails={true} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

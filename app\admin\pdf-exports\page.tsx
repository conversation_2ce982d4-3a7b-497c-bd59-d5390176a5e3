"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { DateRangePickerWithPresets } from "@/components/ui/date-range-picker"
import { DeleteConfirmationDialog, ExportConfirmationDialog } from "@/components/ui/confirmation-dialog"
import {
  FileText,
  Download,
  Award,
  BarChart3,
  Settings,
  Eye,
  Users,
  TrendingUp,
  Clock,
  Target,
  CheckCircle,
  XCircle,
  Trash2,
  Loader2,
  Calendar,
  Filter,
  RefreshCw
} from "lucide-react"
import { motion } from "framer-motion"
import { PDFTemplateSelector, PDFPreview } from "@/components/pdf/pdf-preview"
import { PreviewDataFetcher, formatPreviewSummary } from "@/lib/preview-data-fetcher"
import { toast } from "@/lib/toast-utils"
import { DateRange } from "react-day-picker"

interface PdfExport {
  id: string
  type: 'quiz-result' | 'analytics' | 'certificate' | 'bulk'
  filename: string
  size: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  options: any
  error?: string
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    name: string
    email: string
  }
}

interface PdfExportStats {
  overview: {
    totalExports: number
    completedExports: number
    failedExports: number
    pendingExports: number
    successRate: number
    totalSize: string
    totalSizeBytes: number
  }
  exportsByType: Array<{
    type: string
    count: number
  }>
  recentExports: Array<{
    id: string
    type: string
    filename: string
    size: number
    status: string
    createdAt: string
    user?: {
      id: string
      name: string
      email: string
    }
  }>
  period: string
}





export default function PDFExportsPage() {
  const [selectedTemplate, setSelectedTemplate] = useState(() => {
    // Load saved template from localStorage
    if (typeof window !== 'undefined') {
      return localStorage.getItem('pdf-template-preference') || "modern"
    }
    return "modern"
  })
  const [activePreview, setActivePreview] = useState<string | null>(null)
  const [exports, setExports] = useState<PdfExport[]>([])
  const [stats, setStats] = useState<PdfExportStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [availableQuizzes, setAvailableQuizzes] = useState<any[]>([])
  const [availableStudents, setAvailableStudents] = useState<any[]>([])
  const [refreshing, setRefreshing] = useState(false)
  const [period, setPeriod] = useState<'7d' | '30d' | '90d' | 'all'>('30d')
  const [selectedQuizId, setSelectedQuizId] = useState<string>('')
  const [selectedUserId, setSelectedUserId] = useState<string>('')

  // Enhanced state for new functionality
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [bulkExportLoading, setBulkExportLoading] = useState<{[key: string]: boolean}>({})
  const [previewLoading, setPreviewLoading] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const [customTemplate, setCustomTemplate] = useState({
    headerText: '',
    footerText: '',
    logoUrl: '',
    backgroundColor: '#ffffff',
    textColor: '#000000'
  })

  // Preview state
  const [previewData, setPreviewData] = useState<{[key: string]: any}>({})
  const [previewValidation, setPreviewValidation] = useState<{[key: string]: any}>({})
  const [previewError, setPreviewError] = useState<string>('')

  // Handle template selection with persistence
  const handleTemplateSelect = (template: string) => {
    setSelectedTemplate(template)
    // Save to localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem('pdf-template-preference', template)
    }
    toast.success(`Template changed to ${template}`)
  }

  // Fetch real data for dropdowns
  const fetchAvailableData = async () => {
    try {
      const [quizzesRes, studentsRes] = await Promise.all([
        fetch('/api/admin/quizzes?status=published'),
        fetch('/api/admin/users?role=STUDENT&limit=50')
      ])

      if (quizzesRes.ok) {
        const quizzesData = await quizzesRes.json()
        setAvailableQuizzes(quizzesData.quizzes || [])
      }

      if (studentsRes.ok) {
        const studentsData = await studentsRes.json()
        setAvailableStudents(studentsData.data?.users || [])
      } else {
        console.error('Failed to fetch students:', studentsRes.status, studentsRes.statusText)
        const errorData = await studentsRes.json().catch(() => null)
        console.error('Error details:', errorData)
      }
    } catch (error) {
      console.error('Error fetching available data:', error)
    }
  }
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'processing' | 'completed' | 'failed'>('all')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedExports, setSelectedExports] = useState<string[]>([])

  useEffect(() => {
    fetchExports()
    fetchStats()
    fetchAvailableData()
  }, [currentPage, statusFilter, typeFilter, period])

  const fetchExports = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        status: statusFilter,
        ...(typeFilter && { type: typeFilter })
      })

      const response = await fetch(`/api/admin/pdf-exports?${params}`)
      if (!response.ok) throw new Error('Failed to fetch exports')

      const data = await response.json()
      setExports(data.data.exports)
      setTotalPages(data.data.pagination.totalPages)
    } catch (error) {
      console.error('Error fetching exports:', error)
      toast.error('Failed to load PDF exports')
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch(`/api/admin/pdf-exports/stats?period=${period}`)
      if (!response.ok) throw new Error('Failed to fetch stats')

      const data = await response.json()
      setStats(data.data)
    } catch (error) {
      console.error('Error fetching stats:', error)
      toast.error('Failed to load statistics')
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await Promise.all([fetchExports(), fetchStats()])
    setRefreshing(false)
    toast.success('Data refreshed')
  }

  const handleCreateExport = async (type: string, options: any) => {
    try {
      const response = await fetch('/api/admin/pdf-exports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          filename: `${type}-${Date.now()}.pdf`,
          options
        })
      })

      if (!response.ok) {
        const error = await response.json()
        console.error('Export creation failed:', error)
        throw new Error(error.error?.message || 'Failed to create export')
      }

      const data = await response.json()
             setExports(prev => [data.data, ...prev])
      toast.success(`${type.charAt(0).toUpperCase() + type.slice(1)} export created successfully! Processing in background...`)

      // Refresh the exports list after a short delay to show updated status
      setTimeout(() => {
        fetchExports()
      }, 3000)

    } catch (error: any) {
      console.error('Error creating export:', error)
      toast.error(error.message || 'Failed to create export')
    }
  }

  const handleDeleteExport = async (exportId: string) => {
    try {
      const response = await fetch(`/api/admin/pdf-exports/${exportId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Failed to delete export')
      }

      setExports(prev => prev.filter(exp => exp.id !== exportId))
      toast.success('Export deleted successfully')
    } catch (error: any) {
      console.error('Error deleting export:', error)
      toast.error(error.message || 'Failed to delete export')
    }
  }

  // Validation functions
  const validateQuizResultExport = () => {
    const errors: {[key: string]: string} = {}
    if (!selectedQuizId) errors.quiz = 'Please select a quiz'
    if (!selectedUserId) errors.student = 'Please select a student'
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const validateAnalyticsExport = () => {
    const errors: {[key: string]: string} = {}
    if (!selectedUserId) errors.student = 'Please select a student'
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const validateCertificateExport = () => {
    const errors: {[key: string]: string} = {}
    if (!selectedQuizId) errors.quiz = 'Please select a quiz'
    if (!selectedUserId) errors.student = 'Please select a student'
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Bulk export functions
  const handleBulkExport = async (type: 'all-students' | 'quiz-results' | 'certificates') => {
    setBulkExportLoading(prev => ({ ...prev, [type]: true }))

    try {
      const filters: any = {}

      if (type === 'quiz-results' || type === 'certificates') {
        if (selectedQuizId) filters.quizId = selectedQuizId
        if (dateRange?.from && dateRange?.to) {
          filters.dateRange = {
            start: dateRange.from.toISOString(),
            end: dateRange.to.toISOString()
          }
        }
      }

      const response = await fetch('/api/admin/bulk-export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          filters,
          template: selectedTemplate,
          customOptions: customTemplate
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Failed to create bulk export')
      }

      const data = await response.json()

      // Add to exports list
      setExports(prev => [data.data, ...prev])
      toast.success(`${type.replace('-', ' ')} export started! Processing ${data.data.estimatedItems} items...`)

      // Refresh exports after a delay
      setTimeout(() => {
        fetchExports()
      }, 3000)

    } catch (error: any) {
      console.error('Error creating bulk export:', error)
      toast.error(error.message || 'Failed to create bulk export')
    } finally {
      setBulkExportLoading(prev => ({ ...prev, [type]: false }))
    }
  }

  const handleDownloadExport = async (exportId: string, filename: string) => {
    let loadingToast: string | number | undefined

    try {
      // Show loading state with progress
      loadingToast = toast.loading('Preparing download...', {
        description: 'Generating PDF with real data'
      })

      // Use AbortController to handle potential cancellation
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

      // Directly download the file from the /file endpoint
      const response = await fetch(`/api/admin/pdf-exports/${exportId}/file`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/pdf'
        }
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`
        try {
          const errorText = await response.text()
          if (errorText && errorText.trim()) {
            errorMessage = errorText
          }
        } catch {
          // If we can't read the error text, use the status
          errorMessage = response.statusText || errorMessage
        }
        throw new Error(`Failed to download PDF: ${errorMessage}`)
      }

      // Get the PDF blob
      const blob = await response.blob()

      // Validate that we got a PDF (be more lenient with content type)
      if (blob.size === 0) {
        throw new Error('Received empty file.')
      }

      if (blob.type && !blob.type.includes('pdf') && !blob.type.includes('octet-stream')) {
        console.warn('Unexpected content type:', blob.type)
        // Don't throw error, just warn - some servers return different content types
      }

      // Create a temporary link to download the file
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'
      document.body.appendChild(link)

      // Trigger download
      link.click()

      // Clean up immediately
      document.body.removeChild(link)

      // Dismiss loading toast first
      if (loadingToast) {
        toast.dismiss(loadingToast)
        loadingToast = undefined
      }

      // Show success toast after a small delay to ensure download started
      setTimeout(() => {
        toast.success('PDF downloaded successfully!', {
          description: `File: ${filename}`,
          duration: 3000,
          deduplicationTime: 5000
        })
      }, 500)

      // Clean up blob URL after a longer delay
      setTimeout(() => {
        URL.revokeObjectURL(url)
      }, 2000)

    } catch (error: any) {
      console.error('Error downloading export:', error)

      // Dismiss loading toast if it exists
      if (loadingToast) {
        toast.dismiss(loadingToast)
        loadingToast = undefined
      }

      // Handle specific error types
      let errorMessage = 'Failed to download export'
      if (error.name === 'AbortError') {
        errorMessage = 'Download timed out. Please try again.'
      } else if (error.message) {
        errorMessage = error.message
      }

      toast.error('Download failed', {
        description: errorMessage,
        duration: 5000,
        deduplicationTime: 5000
      })
    }
  }

  // Bulk delete function
  const handleBulkDelete = async (exportIds: string[]) => {
    try {
      const deletePromises = exportIds.map(id =>
        fetch(`/api/admin/pdf-exports/${id}`, { method: 'DELETE' })
      )

      await Promise.all(deletePromises)

      setExports(prev => prev.filter(exp => !exportIds.includes(exp.id)))
      toast.success(`${exportIds.length} export(s) deleted successfully`)
    } catch (error: any) {
      console.error('Error bulk deleting exports:', error)
      toast.error('Failed to delete some exports')
    }
  }

  // Preview functions
  const handlePreviewGeneration = async (type: 'quiz-result' | 'analytics' | 'certificate') => {
    setPreviewLoading(true)
    setPreviewError('')

    // Show loading toast with type-specific message
    const loadingToast = toast.loading(`Generating ${type.replace('-', ' ')} preview...`, {
      description: 'Fetching real data and generating PDF preview'
    })

    try {
      let data: any

      switch (type) {
        case 'quiz-result':
          if (!selectedQuizId || !selectedUserId) {
            throw new Error('Please select both quiz and student')
          }
          data = await PreviewDataFetcher.fetchQuizResultData(selectedQuizId, selectedUserId)
          break

        case 'analytics':
          if (!selectedUserId) {
            throw new Error('Please select a student')
          }
          const range = dateRange ? {
            start: dateRange.from!.toISOString(),
            end: dateRange.to!.toISOString()
          } : undefined
          data = await PreviewDataFetcher.fetchAnalyticsData(selectedUserId, range)
          break

        case 'certificate':
          if (!selectedQuizId || !selectedUserId) {
            throw new Error('Please select both quiz and student')
          }
          data = await PreviewDataFetcher.fetchCertificateData(selectedQuizId, selectedUserId)
          break
      }

      setPreviewData(prev => ({ ...prev, [type]: data }))
      setActivePreview(type)

      toast.dismiss(loadingToast)
      toast.success(`${type.replace('-', ' ')} preview generated successfully!`)

    } catch (error: any) {
      console.error('Preview generation error:', error)
      setPreviewError(error.message || 'Failed to generate preview')
      toast.dismiss(loadingToast)
      toast.error('Preview generation failed', {
        description: error.message || 'Failed to generate preview'
      })
    } finally {
      setPreviewLoading(false)
    }
  }

  const validatePreview = async (type: 'quiz-result' | 'analytics' | 'certificate') => {
    const params = {
      quizId: selectedQuizId,
      userId: selectedUserId,
      dateRange
    }

    const validation = await PreviewDataFetcher.validatePreviewData(type, params)
    setPreviewValidation(prev => ({ ...prev, [type]: validation }))
    return validation
  }

  // Get real data for previews
  const getPreviewData = () => {
    const selectedQuiz = availableQuizzes.find(q => q.id === selectedQuizId)
    const selectedStudent = availableStudents.find(s => s.id === selectedUserId)

    return {
      quiz: selectedQuiz || { title: 'Select a quiz', description: 'No quiz selected' },
      student: selectedStudent || { name: 'Select a student', email: 'No student selected' }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <div className="p-6 space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="relative"
        >
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

          <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    PDF Export System
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Generate and manage PDF exports for quiz results, analytics, and certificates
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <select
                    value={period}
                    onChange={(e) => setPeriod(e.target.value as '7d' | '30d' | '90d' | 'all')}
                    className="px-4 py-2 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 rounded-lg text-sm font-medium"
                  >
                  <option value="7d">Last 7 days</option>
                  <option value="30d">Last 30 days</option>
                  <option value="90d">Last 90 days</option>
                  <option value="all">All time</option>
                </select>
                </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <Download className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.totalExports.toLocaleString() || '0'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Total Exports</p>
              </div>
              <Download className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.completedExports.toLocaleString() || '0'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Completed</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.pendingExports.toLocaleString() || '0'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">In Queue</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    `${stats?.overview.successRate || 0}%`
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Success Rate</p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>
      </div>

        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Tabs defaultValue="exports" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-lg p-2 h-auto">
              <TabsTrigger
                value="exports"
                className="gap-2 h-12 glass data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <FileText className="h-4 w-4" />
                Export Tools
              </TabsTrigger>
              <TabsTrigger
                value="previews"
                className="gap-2 h-12 glass data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <Eye className="h-4 w-4" />
                Previews
              </TabsTrigger>
              <TabsTrigger
                value="templates"
                className="gap-2 h-12 glass data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <Settings className="h-4 w-4" />
                Templates
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="gap-2 h-12 glass data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <Clock className="h-4 w-4" />
                History
              </TabsTrigger>
            </TabsList>

            {/* Export Tools Tab */}
            <TabsContent value="exports" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="grid grid-cols-1 lg:grid-cols-3 gap-8"
              >
                {/* Quiz Result Export */}
                <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl hover:shadow-2xl transition-all duration-300 group">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20 group-hover:scale-110 transition-transform duration-300">
                        <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          Quiz Result Export
                        </CardTitle>
                        <CardDescription className="text-base">
                          Generate detailed quiz result reports with performance analysis
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                    <div>
                      <label className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 block">Select Quiz *</label>
                      <select
                        value={selectedQuizId}
                        onChange={(e) => {
                          setSelectedQuizId(e.target.value)
                          setValidationErrors(prev => ({ ...prev, quiz: '' }))
                        }}
                        className={`w-full px-4 py-3 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 rounded-lg focus:bg-white/60 dark:focus:bg-gray-800/60 transition-all duration-300 ${
                          validationErrors.quiz ? 'border-red-500 bg-red-50/50 dark:bg-red-900/20' : ''
                        }`}
                      >
                      <option value="">Choose a quiz...</option>
                      {availableQuizzes.length === 0 ? (
                        <option value="" disabled>No quizzes available</option>
                      ) : (
                        availableQuizzes.map((quiz) => (
                          <option key={quiz.id} value={quiz.id}>
                            {quiz.title} ({quiz.questionCount || quiz.questions?.length || 0} questions)
                          </option>
                        ))
                      )}
                    </select>
                    {validationErrors.quiz && (
                      <p className="text-sm text-red-500 mt-1">{validationErrors.quiz}</p>
                    )}
                  </div>

                    <div>
                      <label className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 block">Select Student *</label>
                      <select
                        value={selectedUserId}
                        onChange={(e) => {
                          setSelectedUserId(e.target.value)
                          setValidationErrors(prev => ({ ...prev, student: '' }))
                        }}
                        className={`w-full px-4 py-3 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 rounded-lg focus:bg-white/60 dark:focus:bg-gray-800/60 transition-all duration-300 ${
                          validationErrors.student ? 'border-red-500 bg-red-50/50 dark:bg-red-900/20' : ''
                        }`}
                      >
                      <option value="">Choose a student...</option>
                      {availableStudents.length === 0 ? (
                        <option value="" disabled>No students available</option>
                      ) : (
                        availableStudents.map((student) => (
                          <option key={student.id} value={student.id}>
                            {student.name} ({student.email})
                          </option>
                        ))
                      )}
                    </select>
                    {validationErrors.student && (
                      <p className="text-sm text-red-500 mt-1">{validationErrors.student}</p>
                    )}
                  </div>
                </div>

                  <div className="flex gap-3 pt-4">
                    <Button
                      className="flex-1 h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                      onClick={() => {
                        if (validateQuizResultExport()) {
                          handleCreateExport('quiz-result', {
                            quizId: selectedQuizId,
                            userId: selectedUserId,
                            includeAnswers: true,
                            includeStatistics: true,
                            template: selectedTemplate,
                            customOptions: customTemplate
                          })
                        }
                      }}
                    >
                      <Download className="h-5 w-5 mr-2" />
                      Export Quiz Result
                    </Button>
                    <Button
                      variant="outline"
                      className="h-12 px-4 glass hover:bg-white/20 dark:hover:bg-gray-800/20 border-white/20 dark:border-gray-700/20"
                      onClick={() => handlePreviewGeneration('quiz-result')}
                      disabled={!selectedQuizId || !selectedUserId || previewLoading}
                    >
                      {previewLoading && activePreview === 'quiz-result' ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
              </CardContent>
            </Card>

                {/* Analytics Export */}
                <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl hover:shadow-2xl transition-all duration-300 group">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-br from-green-500/20 to-emerald-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20 group-hover:scale-110 transition-transform duration-300">
                        <BarChart3 className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                          Analytics Export
                        </CardTitle>
                        <CardDescription className="text-base">
                          Export comprehensive learning analytics and performance reports
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 block">Select Student for Analytics *</label>
                        <select
                          value={selectedUserId}
                          onChange={(e) => {
                            setSelectedUserId(e.target.value)
                            setValidationErrors(prev => ({ ...prev, student: '' }))
                          }}
                          className={`w-full px-4 py-3 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 rounded-lg focus:bg-white/60 dark:focus:bg-gray-800/60 transition-all duration-300 ${
                            validationErrors.student ? 'border-red-500 bg-red-50/50 dark:bg-red-900/20' : ''
                          }`}
                        >
                      <option value="">Choose a student...</option>
                      {availableStudents.length === 0 ? (
                        <option value="" disabled>No students available</option>
                      ) : (
                        availableStudents.map((student) => (
                          <option key={student.id} value={student.id}>
                            {student.name} ({student.email})
                          </option>
                        ))
                      )}
                    </select>
                    {validationErrors.student && (
                      <p className="text-sm text-red-500 mt-1">{validationErrors.student}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Date Range</label>
                    <DateRangePickerWithPresets
                      value={dateRange}
                      onChange={setDateRange}
                      placeholder="Select date range for analytics"
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Leave empty to use last 30 days
                    </p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="default"
                    size="sm"
                    className="flex-1"
                    onClick={() => {
                      if (validateAnalyticsExport()) {
                        const range = dateRange || {
                          from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                          to: new Date()
                        }
                        handleCreateExport('analytics', {
                          userId: selectedUserId,
                          dateRange: {
                            start: range.from!.toISOString(),
                            end: range.to!.toISOString()
                          },
                          includeStatistics: true,
                          template: selectedTemplate,
                          customOptions: customTemplate
                        })
                      }
                    }}
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Export Analytics
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePreviewGeneration('analytics')}
                    disabled={!selectedUserId || previewLoading}
                  >
                    {previewLoading && activePreview === 'analytics' ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

                {/* Certificate Export */}
                <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl hover:shadow-2xl transition-all duration-300 group">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-br from-yellow-500/20 to-orange-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20 group-hover:scale-110 transition-transform duration-300">
                        <Award className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                          Certificate Export
                        </CardTitle>
                        <CardDescription className="text-base">
                          Generate professional certificates for completed quizzes
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium">Select Quiz *</label>
                    <select
                      value={selectedQuizId}
                      onChange={(e) => {
                        setSelectedQuizId(e.target.value)
                        setValidationErrors(prev => ({ ...prev, quiz: '' }))
                      }}
                      className={`w-full mt-1 px-3 py-2 border rounded-md ${
                        validationErrors.quiz ? 'border-red-500' : ''
                      }`}
                    >
                      <option value="">Choose a quiz...</option>
                      {availableQuizzes.length === 0 ? (
                        <option value="" disabled>No quizzes available</option>
                      ) : (
                        availableQuizzes.map((quiz) => (
                          <option key={quiz.id} value={quiz.id}>
                            {quiz.title} (Passing: {quiz.passingScore || 60}%)
                          </option>
                        ))
                      )}
                    </select>
                    {validationErrors.quiz && (
                      <p className="text-sm text-red-500 mt-1">{validationErrors.quiz}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Select Student *</label>
                    <select
                      value={selectedUserId}
                      onChange={(e) => {
                        setSelectedUserId(e.target.value)
                        setValidationErrors(prev => ({ ...prev, student: '' }))
                      }}
                      className={`w-full mt-1 px-3 py-2 border rounded-md ${
                        validationErrors.student ? 'border-red-500' : ''
                      }`}
                    >
                      <option value="">Choose a student...</option>
                      {availableStudents.length === 0 ? (
                        <option value="" disabled>No students available</option>
                      ) : (
                        availableStudents.map((student) => (
                          <option key={student.id} value={student.id}>
                            {student.name} ({student.email})
                          </option>
                        ))
                      )}
                    </select>
                    {validationErrors.student && (
                      <p className="text-sm text-red-500 mt-1">{validationErrors.student}</p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">
                      Only students with passing scores are eligible for certificates
                    </p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="default"
                    size="sm"
                    className="flex-1"
                    onClick={() => {
                      if (validateCertificateExport()) {
                        handleCreateExport('certificate', {
                          quizId: selectedQuizId,
                          userId: selectedUserId,
                          template: selectedTemplate,
                          customOptions: customTemplate
                        })
                      }
                    }}
                  >
                    <Award className="h-4 w-4 mr-2" />
                    Generate Certificate
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePreviewGeneration('certificate')}
                    disabled={!selectedQuizId || !selectedUserId || previewLoading}
                  >
                    {previewLoading && activePreview === 'certificate' ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Bulk Export Tools */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-purple-500/20 to-pink-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                    <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Bulk Export Tools
                    </CardTitle>
                    <CardDescription className="text-base">
                      Export multiple documents at once with advanced filtering
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Bulk Export Filters */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div>
                    <label className="text-sm font-medium">Filter by Quiz (Optional)</label>
                    <select
                      value={selectedQuizId}
                      onChange={(e) => setSelectedQuizId(e.target.value)}
                      className="w-full mt-1 px-3 py-2 border rounded-md"
                    >
                      <option value="">All quizzes</option>
                      {availableQuizzes.length === 0 ? (
                        <option value="" disabled>No quizzes available</option>
                      ) : (
                        availableQuizzes.map((quiz) => (
                          <option key={quiz.id} value={quiz.id}>
                            {quiz.title}
                          </option>
                        ))
                      )}
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Date Range (Optional)</label>
                    <DateRangePickerWithPresets
                      value={dateRange}
                      onChange={setDateRange}
                      placeholder="All time"
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <ExportConfirmationDialog
                    trigger={
                      <Button
                        variant="outline"
                        className="h-20 flex-col"
                        disabled={bulkExportLoading['all-students']}
                      >
                        {bulkExportLoading['all-students'] ? (
                          <Loader2 className="h-6 w-6 mb-2 animate-spin" />
                        ) : (
                          <Users className="h-6 w-6 mb-2" />
                        )}
                        <span>Export All Students</span>
                      </Button>
                    }
                    exportType="All Students"
                    itemCount={availableStudents.length}
                    onConfirm={() => handleBulkExport('all-students')}
                    disabled={bulkExportLoading['all-students']}
                  />

                  <ExportConfirmationDialog
                    trigger={
                      <Button
                        variant="outline"
                        className="h-20 flex-col"
                        disabled={bulkExportLoading['quiz-results']}
                      >
                        {bulkExportLoading['quiz-results'] ? (
                          <Loader2 className="h-6 w-6 mb-2 animate-spin" />
                        ) : (
                          <FileText className="h-6 w-6 mb-2" />
                        )}
                        <span>Export Quiz Results</span>
                      </Button>
                    }
                    exportType="Quiz Results"
                    onConfirm={() => handleBulkExport('quiz-results')}
                    disabled={bulkExportLoading['quiz-results']}
                  />

                  <ExportConfirmationDialog
                    trigger={
                      <Button
                        variant="outline"
                        className="h-20 flex-col"
                        disabled={bulkExportLoading['certificates']}
                      >
                        {bulkExportLoading['certificates'] ? (
                          <Loader2 className="h-6 w-6 mb-2 animate-spin" />
                        ) : (
                          <Award className="h-6 w-6 mb-2" />
                        )}
                        <span>Export Certificates</span>
                      </Button>
                    }
                    exportType="Certificates"
                    onConfirm={() => handleBulkExport('certificates')}
                    disabled={bulkExportLoading['certificates']}
                  />
                </div>

                <div className="text-sm text-muted-foreground">
                  <p>• All Students: Export user profiles and statistics</p>
                  <p>• Quiz Results: Export completed quiz attempts with scores</p>
                  <p>• Certificates: Export certificates for passing students</p>
                </div>
              </div>
              </CardContent>
            </Card>
              </motion.div>
            </TabsContent>

            {/* Previews Tab */}
            <TabsContent value="previews" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="space-y-6"
              >
                {previewError && (
                  <Card className="glass bg-red-50/60 dark:bg-red-900/20 backdrop-blur-sm border-red-200/50 dark:border-red-800/50 shadow-xl">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-100 dark:bg-red-900/40 rounded-lg">
                          <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                        </div>
                        <div>
                          <span className="font-semibold text-red-700 dark:text-red-300">Preview Error</span>
                          <p className="text-red-600 dark:text-red-400 mt-1">{previewError}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

          {activePreview === 'quiz-result' && previewData['quiz-result'] && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    Quiz Result Preview
                  </CardTitle>
                  <CardDescription>
                    {(() => {
                      const summary = formatPreviewSummary('quiz-result', previewData['quiz-result'])
                      return (
                        <div>
                          <p>{summary.subtitle}</p>
                          <div className="mt-2 space-y-1">
                            {summary.details.map((detail, index) => (
                              <p key={`quiz-result-${index}`} className="text-xs">{detail}</p>
                            ))}
                          </div>
                        </div>
                      )
                    })()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PDFPreview
                    type="quiz-result"
                    data={previewData['quiz-result']}
                    template={selectedTemplate}
                  />
                </CardContent>
              </Card>
            </div>
          )}

          {activePreview === 'analytics' && previewData['analytics'] && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                    Analytics Preview
                  </CardTitle>
                  <CardDescription>
                    {(() => {
                      const summary = formatPreviewSummary('analytics', previewData['analytics'])
                      return (
                        <div>
                          <p>{summary.subtitle}</p>
                          <div className="mt-2 space-y-1">
                            {summary.details.map((detail, index) => (
                              <p key={`analytics-${index}`} className="text-xs">{detail}</p>
                            ))}
                          </div>
                        </div>
                      )
                    })()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PDFPreview
                    type="analytics"
                    data={previewData['analytics']}
                    template={selectedTemplate}
                  />
                </CardContent>
              </Card>
            </div>
          )}

          {activePreview === 'certificate' && previewData['certificate'] && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5 text-yellow-600" />
                    Certificate Preview
                  </CardTitle>
                  <CardDescription>
                    {(() => {
                      const summary = formatPreviewSummary('certificate', previewData['certificate'])
                      return (
                        <div>
                          <p>{summary.subtitle}</p>
                          <div className="mt-2 space-y-1">
                            {summary.details.map((detail, index) => (
                              <p key={`certificate-${index}`} className="text-xs">{detail}</p>
                            ))}
                          </div>
                        </div>
                      )
                    })()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PDFPreview
                    type="certificate"
                    data={previewData['certificate']}
                    template={selectedTemplate}
                  />
                </CardContent>
              </Card>
            </div>
          )}

                {!activePreview && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center py-12">
                        <Eye className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-xl font-semibold mb-2">No Preview Selected</h3>
                        <p className="text-muted-foreground mb-6">
                          Use the preview buttons in the Export Tools tab to generate real-time previews
                        </p>
                        <div className="flex justify-center gap-2">
                          <Button
                            variant="outline"
                            onClick={() => handlePreviewGeneration('quiz-result')}
                            disabled={!selectedQuizId || !selectedUserId || previewLoading}
                          >
                            {previewLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                            Quiz Result
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => handlePreviewGeneration('analytics')}
                            disabled={!selectedUserId || previewLoading}
                          >
                            {previewLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                            Analytics
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => handlePreviewGeneration('certificate')}
                            disabled={!selectedQuizId || !selectedUserId || previewLoading}
                          >
                            {previewLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                            Certificate
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </motion.div>
            </TabsContent>

            {/* Templates Tab */}
            <TabsContent value="templates" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="space-y-6"
              >
                <PDFTemplateSelector
                  onTemplateSelect={handleTemplateSelect}
                  selectedTemplate={selectedTemplate}
                />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Template Customization */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900 dark:to-gray-900">
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Template Customization
                </CardTitle>
                <CardDescription>
                  Customize colors, branding, and layout for your PDF exports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6 p-6">
                <div className="flex items-center justify-between p-4 bg-primary/5 rounded-lg border">
                  <div>
                    <label className="text-sm font-medium">Active Template</label>
                    <div className="mt-1">
                      <Badge variant="default" className="capitalize font-medium">
                        {selectedTemplate.replace('-', ' ')}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-muted-foreground">Applied to all exports</div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Background Color</label>
                    <div className="mt-1 flex items-center gap-2">
                      <input
                        type="color"
                        value={customTemplate.backgroundColor}
                        onChange={(e) => setCustomTemplate(prev => ({ ...prev, backgroundColor: e.target.value }))}
                        className="w-12 h-8 border rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={customTemplate.backgroundColor}
                        onChange={(e) => setCustomTemplate(prev => ({ ...prev, backgroundColor: e.target.value }))}
                        className="flex-1 px-2 py-1 text-sm border rounded"
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Text Color</label>
                    <div className="mt-1 flex items-center gap-2">
                      <input
                        type="color"
                        value={customTemplate.textColor}
                        onChange={(e) => setCustomTemplate(prev => ({ ...prev, textColor: e.target.value }))}
                        className="w-12 h-8 border rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={customTemplate.textColor}
                        onChange={(e) => setCustomTemplate(prev => ({ ...prev, textColor: e.target.value }))}
                        className="flex-1 px-2 py-1 text-sm border rounded"
                        placeholder="#000000"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Logo URL (Optional)</label>
                  <input
                    type="url"
                    value={customTemplate.logoUrl}
                    onChange={(e) => setCustomTemplate(prev => ({ ...prev, logoUrl: e.target.value }))}
                    className="w-full mt-1 px-3 py-2 border rounded-md"
                    placeholder="https://example.com/logo.png"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    URL to your organization`&apos;`s logo (PNG, JPG, or SVG)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Header & Footer Customization */}
            <Card>
              <CardHeader>
                <CardTitle>Header & Footer</CardTitle>
                <CardDescription>
                  Add custom header and footer text to your PDF exports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Custom Header Text</label>
                  <textarea
                    value={customTemplate.headerText}
                    onChange={(e) => setCustomTemplate(prev => ({ ...prev, headerText: e.target.value }))}
                    className="w-full mt-1 px-3 py-2 border rounded-md"
                    rows={3}
                    placeholder="Enter custom header text (e.g., organization name, address)"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Custom Footer Text</label>
                  <textarea
                    value={customTemplate.footerText}
                    onChange={(e) => setCustomTemplate(prev => ({ ...prev, footerText: e.target.value }))}
                    className="w-full mt-1 px-3 py-2 border rounded-md"
                    rows={3}
                    placeholder="Enter custom footer text (e.g., copyright, contact info)"
                  />
                </div>

                <div className="p-3 bg-muted/50 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Available Variables</h4>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p><code>{'{{date}}'}</code> - Current date</p>
                    <p><code>{'{{time}}'}</code> - Current time</p>
                    <p><code>{'{{student?.name || "Unknown Student"}}'}</code> - Student name</p>
                    <p><code>{'{{quiz}}'}</code> - Quiz title</p>
                    <p><code>{'{{score}}'}</code> - Quiz score</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Template Preview */}
          <Card className="overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Live Template Preview
              </CardTitle>
              <CardDescription>
                Real-time preview of your customizations across different document types
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                  <Badge variant="outline" className="mb-2">Quiz Result</Badge>
                  <div className="text-xs text-muted-foreground">Certificate & Reports</div>
                </div>
                <div className="text-center">
                  <Badge variant="outline" className="mb-2">Analytics</Badge>
                  <div className="text-xs text-muted-foreground">Performance Reports</div>
                </div>
                <div className="text-center">
                  <Badge variant="outline" className="mb-2">Certificate</Badge>
                  <div className="text-xs text-muted-foreground">Achievement Awards</div>
                </div>
              </div>

              <div
                className="border-2 border-dashed rounded-xl p-8 min-h-[300px] transition-all duration-200"
                style={{
                  backgroundColor: customTemplate.backgroundColor,
                  color: customTemplate.textColor,
                  borderColor: customTemplate.textColor + '40'
                }}
              >
                {/* Header Section */}
                {customTemplate.headerText && (
                  <div className="border-b-2 pb-4 mb-6 text-center" style={{ borderColor: customTemplate.textColor + '20' }}>
                    <div className="text-lg font-bold">{customTemplate.headerText}</div>
                  </div>
                )}

                {/* Logo Section */}
                {customTemplate.logoUrl && (
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 mx-auto bg-gray-200 rounded-lg flex items-center justify-center">
                      <img src={customTemplate.logoUrl} alt="Logo" className="max-w-full max-h-full" onError={(e) => {
                        e.currentTarget.style.display = 'none'
                        const nextEl = e.currentTarget.nextElementSibling as HTMLElement
                        if (nextEl) nextEl.style.display = 'block'
                      }} />
                      <div style={{ display: 'none' }} className="text-xs text-gray-500">Logo</div>
                    </div>
                  </div>
                )}

                {/* Content Section */}
                <div className="space-y-6">
                  <div className="text-center">
                    <h2 className="text-2xl font-bold mb-2">Sample Document Title</h2>
                    <p className="text-sm opacity-75">Generated with {selectedTemplate.replace('-', ' ')} template</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 rounded-lg" style={{ backgroundColor: customTemplate.textColor + '10' }}>
                        <span className="font-medium">Student Name:</span>
                        <span>John Doe</span>
                      </div>
                      <div className="flex justify-between items-center p-3 rounded-lg" style={{ backgroundColor: customTemplate.textColor + '10' }}>
                        <span className="font-medium">Quiz Title:</span>
                        <span>Sample Assessment</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 rounded-lg" style={{ backgroundColor: customTemplate.textColor + '10' }}>
                        <span className="font-medium">Score:</span>
                        <span className="font-bold text-green-600">85%</span>
                      </div>
                      <div className="flex justify-between items-center p-3 rounded-lg" style={{ backgroundColor: customTemplate.textColor + '10' }}>
                        <span className="font-medium">Date:</span>
                        <span>{new Date().toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Sample Chart/Graph Area */}
                  <div className="mt-6 p-4 rounded-lg" style={{ backgroundColor: customTemplate.textColor + '05' }}>
                    <div className="text-center text-sm opacity-75 mb-3">Sample Chart Area</div>
                    <div className="flex justify-center items-end space-x-2 h-20">
                      {[40, 65, 80, 85, 70].map((height, index) => (
                        <div
                          key={index}
                          className="w-8 rounded-t"
                          style={{
                            height: `${height}%`,
                            backgroundColor: customTemplate.textColor + '40'
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Footer Section */}
                {customTemplate.footerText && (
                  <div className="border-t-2 pt-4 mt-6 text-center text-sm" style={{ borderColor: customTemplate.textColor + '20' }}>
                    {customTemplate.footerText}
                  </div>
                )}
              </div>

              {/* Template Info */}
              <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    Template: <span className="font-medium capitalize">{selectedTemplate.replace('-', ' ')}</span>
                  </span>
                  <span className="text-muted-foreground">
                    Last updated: {new Date().toLocaleTimeString()}
                  </span>
                </div>
              </div>
              </CardContent>
            </Card>
              </motion.div>
            </TabsContent>

            {/* History Tab */}
            <TabsContent value="history" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="space-y-6"
              >
                {/* Enhanced Filters */}
                <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-br from-orange-500/20 to-red-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                        <Filter className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                          Export History & Filters
                        </CardTitle>
                        <CardDescription className="text-base">
                          View and manage your PDF export history with advanced filtering
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap items-center gap-4 pt-4">
                        <div className="flex-1 min-w-[200px] relative">
                          <Filter className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <input
                            type="text"
                            placeholder="Search exports by filename..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-full pl-10 pr-4 py-3 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 rounded-lg focus:bg-white/60 dark:focus:bg-gray-800/60 transition-all duration-300"
                          />
                        </div>

                        <select
                          value={statusFilter}
                          onChange={(e) => setStatusFilter(e.target.value as any)}
                          className="px-4 py-3 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 rounded-lg focus:bg-white/60 dark:focus:bg-gray-800/60 transition-all duration-300"
                        >
                          <option value="all">All Status</option>
                          <option value="pending">Pending</option>
                          <option value="processing">Processing</option>
                          <option value="completed">Completed</option>
                          <option value="failed">Failed</option>
                        </select>

                        <select
                          value={typeFilter}
                          onChange={(e) => setTypeFilter(e.target.value)}
                          className="px-4 py-3 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 rounded-lg focus:bg-white/60 dark:focus:bg-gray-800/60 transition-all duration-300"
                        >
                          <option value="">All Types</option>
                          <option value="quiz-result">Quiz Results</option>
                          <option value="analytics">Analytics</option>
                          <option value="certificate">Certificates</option>
                          <option value="bulk">Bulk Exports</option>
                        </select>

                        <Button
                          variant="outline"
                          onClick={() => {
                            setSearchQuery('')
                            setStatusFilter('all')
                            setTypeFilter('')
                            setSelectedExports([])
                          }}
                          className="px-6 py-3 glass hover:bg-white/20 dark:hover:bg-gray-800/20 border-white/20 dark:border-gray-700/20"
                        >
                          <Filter className="h-4 w-4 mr-2" />
                          Clear Filters
                        </Button>
                      </div>

                    {selectedExports.length > 0 && (
                      <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                        <span className="text-sm font-medium">
                          {selectedExports.length} export(s) selected
                        </span>
                        <div className="flex gap-2 ml-auto">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedExports([])}
                          >
                            Clear Selection
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleBulkDelete(selectedExports)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Selected
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Export List */}
                <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                        <Clock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          Export History
                        </CardTitle>
                        <CardDescription className="text-base">
                          Recent PDF exports and their status
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
                        <div className="flex items-center gap-4">
                          <div className="w-8 h-8 bg-muted rounded"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-muted rounded w-48 mb-2"></div>
                            <div className="h-3 bg-muted rounded w-32"></div>
                          </div>
                        </div>
                        <div className="h-6 w-20 bg-muted rounded"></div>
                      </div>
                    ))}
                  </div>
                ) : exports.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No exports found</h3>
                    <p className="text-muted-foreground">
                      No PDF exports match your current filters.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {exports.map((exportItem) => (
                      <div key={exportItem.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <input
                            type="checkbox"
                            checked={selectedExports.includes(exportItem.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedExports(prev => [...prev, exportItem.id])
                              } else {
                                setSelectedExports(prev => prev.filter(id => id !== exportItem.id))
                              }
                            }}
                            className="w-4 h-4 rounded border-gray-300"
                          />
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            exportItem.status === 'completed' ? 'bg-green-100 text-green-600' :
                            exportItem.status === 'failed' ? 'bg-red-100 text-red-600' :
                            exportItem.status === 'processing' ? 'bg-blue-100 text-blue-600' :
                            'bg-gray-100 text-gray-600'
                          }`}>
                            {exportItem.status === 'completed' && <CheckCircle className="h-4 w-4" />}
                            {exportItem.status === 'failed' && <XCircle className="h-4 w-4" />}
                            {exportItem.status === 'processing' && <Clock className="h-4 w-4" />}
                            {exportItem.status === 'pending' && <Clock className="h-4 w-4" />}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium">{exportItem.filename}</h4>
                            <p className="text-sm text-muted-foreground">
                              {exportItem.type} • {exportItem.user?.name || 'Unknown User'} • {new Date(exportItem.createdAt).toLocaleDateString()}
                            </p>
                            {exportItem.size > 0 && (
                              <p className="text-xs text-muted-foreground">
                                {(exportItem.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            )}
                            {exportItem.error && (
                              <p className="text-xs text-red-500 mt-1">
                                Error: {exportItem.error}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {exportItem.status === 'completed' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDownloadExport(exportItem.id, exportItem.filename)}
                            >
                              <Download className="h-4 w-4 mr-2" />
                              Download
                            </Button>
                          )}
                          <DeleteConfirmationDialog
                            trigger={
                              <Button
                                variant="outline"
                                size="sm"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            }
                            itemName={exportItem.filename}
                            itemType="export"
                            onDelete={() => handleDeleteExport(exportItem.id)}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Pagination */}
                {!loading && exports.length > 0 && totalPages > 1 && (
                  <div className="flex items-center justify-center gap-2 mt-6">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-muted-foreground">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                )}
                </CardContent>
              </Card>
              </motion.div>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
    
                )}
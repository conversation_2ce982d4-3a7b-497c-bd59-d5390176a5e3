'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  LockClosedIcon,
  CheckCircleIcon,
  PlayIcon,
  StarIcon,
  ClockIcon,
  TrophyIcon,
  FireIcon,
  MapIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

interface Mission {
  id: string
  title: string
  description?: string
  icon?: string
  color?: string
  order: number
  xPosition?: number
  yPosition?: number
  isRequired: boolean
  pointsReward: number
  badgeReward?: string
  estimatedTime?: string
  contents: MissionContent[]
  prerequisites: string[]
  progress?: MissionProgress
}

interface MissionContent {
  id: string
  contentId: string
  contentType: 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION'
  order: number
  isRequired: boolean
  title?: string
}

interface MissionProgress {
  id: string
  isStarted: boolean
  isCompleted: boolean
  completionRate: number
  pointsEarned: number
  startedAt?: string
  completedAt?: string
}

interface RoadmapVisualizationProps {
  courseId: string
  courseTitle: string
  roadmapTitle?: string
  roadmapDescription?: string
  missions: Mission[]
  userProgress: {
    totalPoints: number
    completedMissions: number
    currentStreak: number
  }
  onMissionClick: (mission: Mission) => void
  onStartMission: (missionId: string) => void
}

export default function RoadmapVisualization({
  courseId,
  courseTitle,
  roadmapTitle,
  roadmapDescription,
  missions,
  userProgress,
  onMissionClick,
  onStartMission
}: RoadmapVisualizationProps) {
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null)
  const [showMissionDetail, setShowMissionDetail] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Sort missions by order
  const sortedMissions = [...missions].sort((a, b) => a.order - b.order)

  // Calculate mission states
  const getMissionState = (mission: Mission): 'locked' | 'available' | 'in-progress' | 'completed' => {
    if (mission.progress?.isCompleted) return 'completed'
    if (mission.progress?.isStarted) return 'in-progress'
    
    // Check if prerequisites are met
    const prerequisitesMet = mission.prerequisites.every(prereqId => {
      const prereqMission = missions.find(m => m.id === prereqId)
      return prereqMission?.progress?.isCompleted
    })
    
    if (!prerequisitesMet) return 'locked'
    return 'available'
  }

  const getMissionIcon = (mission: Mission, state: string) => {
    if (state === 'completed') {
      return <CheckCircleIconSolid className="w-6 h-6 text-white" />
    }
    if (state === 'locked') {
      return <LockClosedIcon className="w-6 h-6 text-gray-400" />
    }
    if (state === 'in-progress') {
      return <PlayIcon className="w-6 h-6 text-white" />
    }
    return <span className="text-2xl">{mission.icon || '🎯'}</span>
  }

  const getMissionColor = (mission: Mission, state: string) => {
    if (state === 'completed') return '#10B981' // green
    if (state === 'locked') return '#9CA3AF' // gray
    if (state === 'in-progress') return '#3B82F6' // blue
    return mission.color || '#6366F1' // default purple
  }

  const handleMissionClick = (mission: Mission) => {
    const state = getMissionState(mission)
    if (state === 'locked') return
    
    setSelectedMission(mission)
    setShowMissionDetail(true)
    onMissionClick(mission)
  }

  const handleStartMission = async (mission: Mission) => {
    try {
      setLoading(true)
      setError(null)
      await onStartMission(mission.id)
      setShowMissionDetail(false)
    } catch (error: any) {
      setError(error.message || 'Failed to start mission')
    } finally {
      setLoading(false)
    }
  }

  // Show empty state if no missions
  if (missions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-24 h-24 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <MapIcon className="w-12 h-12 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">No Missions Yet</h2>
          <p className="text-gray-600 mb-6">
            This course doesn't have any missions configured yet. Check back later or contact your instructor.
          </p>
        </div>
      </div>
    )
  }

  // Show error state if there's an error
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <XMarkIcon className="w-12 h-12 text-red-500" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-400/20 to-orange-400/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
            {roadmapTitle || `${courseTitle} Learning Journey`}
          </h1>
          {roadmapDescription && (
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
              {roadmapDescription}
            </p>
          )}
          
          {/* Progress Stats */}
          <div className="flex items-center justify-center space-x-8 mb-8">
            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full mb-2">
                <StarIcon className="w-8 h-8 text-white" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{userProgress.totalPoints}</p>
              <p className="text-sm text-gray-600">Points</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full mb-2">
                <TrophyIcon className="w-8 h-8 text-white" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{userProgress.completedMissions}</p>
              <p className="text-sm text-gray-600">Completed</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-400 to-pink-500 rounded-full mb-2">
                <FireIcon className="w-8 h-8 text-white" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{userProgress.currentStreak}</p>
              <p className="text-sm text-gray-600">Day Streak</p>
            </div>
          </div>
        </motion.div>

        {/* Roadmap Path */}
        <div className="relative">
          {/* Path Line */}
          <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
            <defs>
              <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#8B5CF6" stopOpacity="0.3" />
                <stop offset="100%" stopColor="#EC4899" stopOpacity="0.3" />
              </linearGradient>
            </defs>
            
            {/* Draw path between missions */}
            {sortedMissions.map((mission, index) => {
              if (index === sortedMissions.length - 1) return null
              
              const nextMission = sortedMissions[index + 1]
              const startX = (index % 3) * 300 + 150
              const startY = Math.floor(index / 3) * 200 + 100
              const endX = ((index + 1) % 3) * 300 + 150
              const endY = Math.floor((index + 1) / 3) * 200 + 100
              
              return (
                <motion.path
                  key={`path-${mission.id}`}
                  d={`M ${startX} ${startY} Q ${(startX + endX) / 2} ${(startY + endY) / 2 - 50} ${endX} ${endY}`}
                  stroke="url(#pathGradient)"
                  strokeWidth="4"
                  fill="none"
                  strokeDasharray="10,5"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 1, delay: index * 0.2 }}
                />
              )
            })}
          </svg>

          {/* Mission Nodes */}
          <div className="relative" style={{ zIndex: 2 }}>
            {sortedMissions.map((mission, index) => {
              const state = getMissionState(mission)
              const color = getMissionColor(mission, state)
              const x = (index % 3) * 300
              const y = Math.floor(index / 3) * 200
              
              return (
                <motion.div
                  key={mission.id}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="absolute"
                  style={{ 
                    left: `${x}px`, 
                    top: `${y}px`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <MissionNode
                    mission={mission}
                    state={state}
                    color={color}
                    onClick={() => handleMissionClick(mission)}
                  />
                </motion.div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Mission Detail Modal */}
      <AnimatePresence>
        {showMissionDetail && selectedMission && (
          <MissionDetailModal
            mission={selectedMission}
            state={getMissionState(selectedMission)}
            loading={loading}
            onClose={() => setShowMissionDetail(false)}
            onStart={() => handleStartMission(selectedMission)}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

interface MissionNodeProps {
  mission: Mission
  state: 'locked' | 'available' | 'in-progress' | 'completed'
  color: string
  onClick: () => void
}

function MissionNode({ mission, state, color, onClick }: MissionNodeProps) {
  const isClickable = state !== 'locked'

  return (
    <motion.div
      whileHover={isClickable ? { scale: 1.1 } : {}}
      whileTap={isClickable ? { scale: 0.95 } : {}}
      className={`relative ${isClickable ? 'cursor-pointer' : 'cursor-not-allowed'}`}
      onClick={onClick}
      role="button"
      tabIndex={isClickable ? 0 : -1}
      aria-label={`${mission.title} - ${state} mission`}
      aria-describedby={`mission-${mission.id}-description`}
      onKeyDown={(e) => {
        if (isClickable && (e.key === 'Enter' || e.key === ' ')) {
          e.preventDefault()
          onClick()
        }
      }}
    >
      {/* Mission Circle - Minimum 44px touch target for mobile */}
      <div
        className={`w-20 h-20 md:w-24 md:h-24 rounded-full flex items-center justify-center shadow-lg border-4 ${
          state === 'locked'
            ? 'border-gray-300 bg-gray-100'
            : 'border-white bg-white'
        } transition-all duration-200 focus-within:ring-4 focus-within:ring-blue-500 focus-within:ring-opacity-50`}
        style={{
          backgroundColor: state === 'locked' ? '#F3F4F6' : color,
          boxShadow: state === 'locked' ? 'none' : `0 8px 32px ${color}40`,
          minWidth: '44px',
          minHeight: '44px'
        }}
      >
        {getMissionIcon(mission, state)}
      </div>

      {/* Screen reader description */}
      <div id={`mission-${mission.id}-description`} className="sr-only">
        {mission.description}
        {mission.pointsReward > 0 && ` Rewards ${mission.pointsReward} points.`}
        {mission.estimatedTime && ` Estimated time: ${mission.estimatedTime}.`}
        {state === 'locked' && ' This mission is locked. Complete prerequisites first.'}
        {state === 'completed' && ' This mission is completed.'}
        {state === 'in-progress' && ` This mission is ${Math.round(mission.progress?.completionRate || 0)}% complete.`}
      </div>
      
      {/* Progress Ring */}
      {state === 'in-progress' && mission.progress && (
        <svg className="absolute inset-0 w-20 h-20 -rotate-90">
          <circle
            cx="40"
            cy="40"
            r="36"
            stroke="rgba(255,255,255,0.3)"
            strokeWidth="4"
            fill="none"
          />
          <motion.circle
            cx="40"
            cy="40"
            r="36"
            stroke="white"
            strokeWidth="4"
            fill="none"
            strokeLinecap="round"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: mission.progress.completionRate / 100 }}
            transition={{ duration: 1 }}
            style={{
              strokeDasharray: `${2 * Math.PI * 36}`,
              strokeDashoffset: `${2 * Math.PI * 36 * (1 - mission.progress.completionRate / 100)}`
            }}
          />
        </svg>
      )}
      
      {/* Mission Title */}
      <div className="absolute top-24 left-1/2 transform -translate-x-1/2 text-center">
        <p className={`text-sm font-medium ${
          state === 'locked' ? 'text-gray-400' : 'text-gray-800'
        }`}>
          {mission.title}
        </p>
        {mission.pointsReward > 0 && (
          <div className="flex items-center justify-center mt-1">
            <StarIcon className="w-3 h-3 text-yellow-500 mr-1" />
            <span className="text-xs text-gray-600">{mission.pointsReward}</span>
          </div>
        )}
      </div>
      
      {/* Completion Badge */}
      {state === 'completed' && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg"
        >
          <CheckCircleIconSolid className="w-5 h-5 text-white" />
        </motion.div>
      )}
    </motion.div>
  )
}

interface MissionDetailModalProps {
  mission: Mission
  state: 'locked' | 'available' | 'in-progress' | 'completed'
  loading?: boolean
  onClose: () => void
  onStart: () => void
}

function MissionDetailModal({ mission, state, loading = false, onClose, onStart }: MissionDetailModalProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="text-center mb-6">
          <div 
            className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center"
            style={{ backgroundColor: mission.color || '#6366F1' }}
          >
            <span className="text-3xl">{mission.icon || '🎯'}</span>
          </div>
          
          <h3 className="text-2xl font-bold text-gray-900 mb-2">{mission.title}</h3>
          {mission.description && (
            <p className="text-gray-600">{mission.description}</p>
          )}
        </div>
        
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Content Items</span>
            <span className="text-sm font-medium">{mission.contents.length}</span>
          </div>
          
          {mission.estimatedTime && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Estimated Time</span>
              <span className="text-sm font-medium">{mission.estimatedTime}</span>
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Points Reward</span>
            <div className="flex items-center">
              <StarIcon className="w-4 h-4 text-yellow-500 mr-1" />
              <span className="text-sm font-medium">{mission.pointsReward}</span>
            </div>
          </div>
          
          {mission.progress && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Progress</span>
                <span className="text-sm font-medium">{Math.round(mission.progress.completionRate)}%</span>
              </div>
              <Progress value={mission.progress.completionRate} className="h-2" />
            </div>
          )}
        </div>
        
        <div className="flex space-x-3">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Close
          </Button>
          {state === 'available' && (
            <Button onClick={onStart} className="flex-1" disabled={loading}>
              {loading ? 'Starting...' : 'Start Mission'}
            </Button>
          )}
          {state === 'in-progress' && (
            <Button onClick={onStart} className="flex-1" disabled={loading}>
              {loading ? 'Loading...' : 'Continue'}
            </Button>
          )}
          {state === 'completed' && (
            <Button onClick={onStart} variant="outline" className="flex-1" disabled={loading}>
              {loading ? 'Loading...' : 'Review'}
            </Button>
          )}
        </div>
      </motion.div>
    </motion.div>
  )
}

function getMissionIcon(mission: Mission, state: string) {
  if (state === 'completed') {
    return <CheckCircleIconSolid className="w-6 h-6 text-white" />
  }
  if (state === 'locked') {
    return <LockClosedIcon className="w-6 h-6 text-gray-400" />
  }
  if (state === 'in-progress') {
    return <PlayIcon className="w-6 h-6 text-white" />
  }
  return <span className="text-2xl">{mission.icon || '🎯'}</span>
}

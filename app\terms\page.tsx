'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, FileText, Users, CreditCard, Shield, AlertTriangle, Scale } from 'lucide-react';
import Link from 'next/link';
import { useSystemSettings } from '@/hooks/use-system-settings';

export default function TermsOfServicePage() {
  const { settings } = useSystemSettings();
  const sections = [
    {
      id: 'acceptance',
      title: 'Acceptance of Terms',
      icon: FileText,
      content: [
        {
          text: `By accessing and using ${settings.companyName} ("the Platform"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.`
        },
        {
          text: `These Terms of Service ("Terms") govern your use of our website, mobile application, and all related services provided by ${settings.companyName}. These Terms apply to all visitors, users, and others who access or use the service.`
        }
      ]
    },
    {
      id: 'user-accounts',
      title: 'User Accounts and Registration',
      icon: Users,
      content: [
        {
          subtitle: 'Account Creation',
          text: 'To access certain features of our platform, you must register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.'
        },
        {
          subtitle: 'Account Security',
          text: 'You are responsible for safeguarding the password and all activities that occur under your account. You agree to immediately notify us of any unauthorized use of your account or any other breach of security.'
        },
        {
          subtitle: 'Account Termination',
          text: 'We reserve the right to terminate or suspend your account at any time, with or without cause, and with or without notice, for conduct that we believe violates these Terms or is harmful to other users, us, or third parties.'
        }
      ]
    },
    {
      id: 'services',
      title: 'Description of Services',
      icon: Shield,
      content: [
        {
          subtitle: 'Educational Content',
          text: `${settings.companyName} provides online educational services including courses, practice tests, study materials, and learning tools for various competitive examinations including JEE, NEET, UPSC, SSC, and others.`
        },
        {
          subtitle: 'Platform Features',
          text: 'Our platform includes features such as video lectures, interactive quizzes, progress tracking, doubt resolution, live classes, and personalized learning recommendations.'
        },
        {
          subtitle: 'Service Availability',
          text: 'We strive to maintain high availability of our services, but we do not guarantee uninterrupted access. We may temporarily suspend services for maintenance, updates, or other operational reasons.'
        }
      ]
    },
    {
      id: 'payment-terms',
      title: 'Payment Terms and Billing',
      icon: CreditCard,
      content: [
        {
          subtitle: 'Course Fees',
          text: 'Course fees are clearly displayed on our platform. All prices are in Indian Rupees (INR) unless otherwise specified. Prices may change at any time, but changes will not affect existing enrollments.'
        },
        {
          subtitle: 'Payment Processing',
          text: 'Payments are processed through secure third-party payment processors. By providing payment information, you authorize us to charge the applicable fees to your chosen payment method.'
        },
        {
          subtitle: 'Refund Policy',
          text: 'Refunds are governed by our separate Refund Policy. Generally, we offer a 7-day money-back guarantee for most courses, subject to certain conditions and restrictions.'
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Link>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Scale className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6">
            Terms of Service
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Please read these terms carefully before using our educational platform and services.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
            Last updated: January 28, 2025
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-2xl p-8 mb-8 border border-gray-200 dark:border-gray-700"
        >
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Welcome to PrepLocus</h2>
          <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
            These Terms of Service (&quot;Terms&quot;) govern your use of the PrepLocus platform and services operated by PrepLocus.
            Our platform provides online educational services to help students prepare for competitive examinations.
          </p>
          <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
            By accessing or using our service, you agree to be bound by these Terms. If you disagree with any part of these terms, 
            then you may not access the service.
          </p>
        </motion.div>

        {/* Main Sections */}
        {sections.map((section, index) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 + index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-2xl p-8 mb-8 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl flex items-center justify-center">
                <section.icon className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                {section.title}
              </h2>
            </div>

            <div className="space-y-6">
              {section.content.map((item, itemIndex) => (
                <div key={itemIndex}>
                  {item.subtitle && (
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {item.subtitle}
                    </h3>
                  )}
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {item.text}
                  </p>
                </div>
              ))}
            </div>
          </motion.div>
        ))}

        {/* Additional Important Sections */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="space-y-8"
        >
          {/* User Conduct */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">User Conduct and Prohibited Activities</h2>
            </div>
            
            <div className="space-y-4">
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                You agree not to engage in any of the following prohibited activities:
              </p>
              <ul className="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2">
                <li>Sharing your account credentials with others</li>
                <li>Copying, distributing, or reselling course content without permission</li>
                <li>Using the platform for any illegal or unauthorized purpose</li>
                <li>Attempting to gain unauthorized access to our systems</li>
                <li>Harassing, abusing, or harming other users</li>
                <li>Uploading malicious code or viruses</li>
                <li>Interfering with the proper functioning of the platform</li>
              </ul>
            </div>
          </div>

          {/* Intellectual Property */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Intellectual Property Rights</h2>
            <div className="space-y-4">
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                All content on the PrepLocus platform, including but not limited to text, graphics, logos, images, videos, 
                audio clips, and software, is the property of PrepLocus or its content suppliers and is protected by 
                copyright and other intellectual property laws.
              </p>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                You may access and use the content solely for your personal, non-commercial educational purposes. 
                You may not reproduce, distribute, modify, or create derivative works from any content without our express written permission.
              </p>
            </div>
          </div>

          {/* Disclaimers */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Disclaimers and Limitation of Liability</h2>
            <div className="space-y-4">
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Our services are provided &quot;as is&quot; without any warranties, express or implied. We do not guarantee that
                our services will be uninterrupted, error-free, or completely secure.
              </p>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                While we strive to provide accurate and up-to-date educational content, we make no guarantees about 
                exam results or academic success. Your performance depends on various factors including your effort, 
                preparation, and individual circumstances.
              </p>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                To the maximum extent permitted by law, PrepLocus shall not be liable for any indirect, incidental, 
                special, or consequential damages arising from your use of our services.
              </p>
            </div>
          </div>

          {/* Governing Law */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Governing Law and Dispute Resolution</h2>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
              These Terms shall be governed by and construed in accordance with the laws of India. Any disputes arising 
              from these Terms or your use of our services shall be subject to the exclusive jurisdiction of the courts in New Delhi, India.
            </p>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
              We encourage you to contact us first to resolve any disputes informally. If we cannot resolve a dispute 
              informally, we both agree to resolve any claims through binding arbitration in accordance with Indian arbitration laws.
            </p>
          </div>

          {/* Changes to Terms */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Changes to Terms</h2>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
              We reserve the right to modify or replace these Terms at any time. If a revision is material, we will 
              provide at least 30 days notice prior to any new terms taking effect. What constitutes a material change 
              will be determined at our sole discretion. By continuing to access or use our service after any revisions 
              become effective, you agree to be bound by the revised terms.
            </p>
          </div>

          {/* Contact Information */}
          <div className="bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl p-8 text-white">
            <h2 className="text-2xl font-bold mb-4">Questions About These Terms?</h2>
            <p className="text-violet-100 mb-4">
              If you have any questions about these Terms of Service, please contact us:
            </p>
            <div className="space-y-2 text-violet-100 mb-6">
              <p>Email: <EMAIL></p>
              <p>Phone: +91 98765 43210</p>
              <p>Address: New Delhi, India</p>
            </div>
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 bg-white text-violet-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Contact Support
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
